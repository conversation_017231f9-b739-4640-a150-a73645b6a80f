"use client"

import { useState } from "react"
import { CalendarDays } from "lucide-react"

// Components
import { BookingStats } from "../components/booking-stats"
import { BookingActionBar } from "./components/booking-action-bar"
import { BookingCalendar } from "./components/booking calender"
import { BookingModal } from "./components/booking-modal"
import { AllBookingsModal } from "./components/all-booking-modal"
import { BookingData } from "./components/booking-data"

// Data
import { bookings, eventCategories } from "./components/data/booking-data"

export default function BookingPage() {
  const [selectedDate, setSelectedDate] = useState(new Date().toISOString().split("T")[0])
  const [showBookingModal, setShowBookingModal] = useState(false)
  const [newBookingDate, setNewBookingDate] = useState("")
  const [newBookingTime, setNewBookingTime] = useState("")

  // Registration form states
  const [customerName, setCustomerName] = useState("")
  const [customerPhone, setCustomerPhone] = useState("")
  const [customerEmail, setCustomerEmail] = useState("")
  const [eventType, setEventType] = useState("")
  const [guestCount, setGuestCount] = useState("")
  const [selectedHall, setSelectedHall] = useState("")
  const [specialRequests, setSpecialRequests] = useState("")

  const [showAllBookingsModal, setShowAllBookingsModal] = useState(false)
  const [selectedEventType, setSelectedEventType] = useState("all")
  const [showCalendar, setShowCalendar] = useState(false)
  const [currentCalendarMonth, setCurrentCalendarMonth] = useState(0)
  const [selectedCalendarDate, setSelectedCalendarDate] = useState<string | null>(null)

  return (
    <>
      <div className="flex-1 overflow-hidden bg-gray-50">
        {/* Header */}
        <header className="flex h-16 items-end justify-between border-2 ml-3 mr-2 rounded-[12px] bg-white px-4 pb-4">
          <div className="flex items-center gap-3">
            <h1 className="text-2xl text-black font-semibold">Booking Management</h1>
          </div>
          <button className="rounded-full bg-gray-100 p-2 hover:bg-gray-200 transition-colors">
            <CalendarDays className="h-7 w-6 text-green-400" />
          </button>
        </header>

        {/* Main Content */}
        <main className="p-4 h-full overflow-auto">
          {/* Stats Cards */}
          <BookingStats />

          {/* Action Bar */}
          <BookingActionBar
            showCalendar={showCalendar}
            setShowCalendar={setShowCalendar}
            setShowBookingModal={setShowBookingModal}
            setShowAllBookingsModal={setShowAllBookingsModal}
          />

          {/* Calendar Section */}
          {showCalendar && (
            <BookingCalendar
              currentCalendarMonth={currentCalendarMonth}
              setCurrentCalendarMonth={setCurrentCalendarMonth}
              selectedDate={selectedDate}
              setSelectedCalendarDate={setSelectedCalendarDate}
              bookings={bookings}
            />
          )}

          {/* Booking Data */}
          <BookingData />
        </main>
      </div>

      {/* Modals */}
      {showBookingModal && (
        <BookingModal
          setShowBookingModal={setShowBookingModal}
          customerName={customerName}
          setCustomerName={setCustomerName}
          customerPhone={customerPhone}
          setCustomerPhone={setCustomerPhone}
          customerEmail={customerEmail}
          setCustomerEmail={setCustomerEmail}
          eventType={eventType}
          setEventType={setEventType}
          guestCount={guestCount}
          setGuestCount={setGuestCount}
          selectedHall={selectedHall}
          setSelectedHall={setSelectedHall}
          specialRequests={specialRequests}
          setSpecialRequests={setSpecialRequests}
          newBookingDate={newBookingDate}
          setNewBookingDate={setNewBookingDate}
          newBookingTime={newBookingTime}
          setNewBookingTime={setNewBookingTime}
          eventCategories={eventCategories}
        />
      )}

      {showAllBookingsModal && (
        <AllBookingsModal
          setShowAllBookingsModal={setShowAllBookingsModal}
          bookings={bookings}
          selectedEventType={selectedEventType}
          setSelectedEventType={setSelectedEventType}
          eventCategories={eventCategories}
        />
      )}
    </>
  )
}
