/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [
  ["path", { d: "M20 3a2 2 0 0 1 2 2v6a1 1 0 0 1-20 0V5a2 2 0 0 1 2-2z", key: "1uodqw" }],
  ["path", { d: "m8 10 4 4 4-4", key: "1mxd5q" }]
];
const Pocket = createLucideIcon("pocket", __iconNode);

export { __iconNode, Pocket as default };
//# sourceMappingURL=pocket.js.map
