// Event categories
export const eventCategories = [
  { value: "all", label: "All Events" },
  { value: "Wedding", label: "Wedding" },
  { value: "Birthday", label: "Birthday" },
  { value: "Milad", label: "Milad" },
  { value: "<PERSON><PERSON><PERSON>", label: "<PERSON><PERSON><PERSON>" },
  { value: "Valima", label: "Valima" },
  { value: "Engagement", label: "Engagement" },
  { value: "Anniversary", label: "Anniversary" },
  { value: "Other", label: "Other" },
]

// Sample booking data with more dates
export const bookings = [
  {
    id: 1,
    customerName: "Ahmed Ali",
    eventType: "Wedding",
    date: "2025-01-10",
    time: "6:00 PM - 11:00 PM",
    hall: "Grand Hall A",
    guests: 300,
    status: "confirmed",
    amount: 150000,
  },
  {
    id: 2,
    customerName: "Fatima Khan",
    eventType: "Engagement",
    date: "2025-01-15",
    time: "4:00 PM - 8:00 PM",
    hall: "Royal Hall B",
    guests: 150,
    status: "pending",
    amount: 80000,
  },
  {
    id: 3,
    customerName: "<PERSON> Sheikh",
    eventType: "Reception",
    date: "2025-02-05",
    time: "7:00 PM - 12:00 AM",
    hall: "Premium Hall C",
    guests: 250,
    status: "confirmed",
    amount: 120000,
  },
  {
    id: 4,
    customerName: "Zara Ahmed",
    eventType: "Wedding",
    date: "2025-02-18",
    time: "5:00 PM - 10:00 PM",
    hall: "Grand Hall A",
    guests: 400,
    status: "confirmed",
    amount: 200000,
  },
  {
    id: 5,
    customerName: "Omar Khan",
    eventType: "Birthday Party",
    date: "2025-03-12",
    time: "3:00 PM - 7:00 PM",
    hall: "Royal Hall B",
    guests: 100,
    status: "pending",
    amount: 50000,
  },
  {
    id: 6,
    customerName: "Test User",
    eventType: "Wedding",
    date: "2025-03-25",
    time: "6:00 PM - 11:00 PM",
    hall: "Grand Hall A",
    guests: 200,
    status: "confirmed",
    amount: 100000,
  },
  // July 2025 (current month example)
  {
    id: 7,
    customerName: "Ali Raza",
    eventType: "Valima",
    date: "2025-07-10",
    time: "6:00 PM - 11:00 PM",
    hall: "Grand Hall A",
    guests: 200,
    status: "confirmed",
    amount: 100000,
  },
  {
    id: 8,
    customerName: "Sana Mir",
    eventType: "Aqiqa",
    date: "2025-07-15",
    time: "2:00 PM - 6:00 PM",
    hall: "Royal Hall B",
    guests: 120,
    status: "pending",
    amount: 60000,
  },
  {
    id: 9,
    customerName: "Bilal Aslam",
    eventType: "Engagement",
    date: "2025-07-22",
    time: "4:00 PM - 8:00 PM",
    hall: "Premium Hall C",
    guests: 180,
    status: "confirmed",
    amount: 90000,
  },
  {
    id: 10,
    customerName: "Ahmed Ali",
    eventType: "Wedding",
    date: "2024-01-15",
    time: "6:00 PM - 11:00 PM",
    hall: "Grand Hall A",
    guests: 300,
    status: "confirmed",
    amount: 150000,
  },
  {
    id: 11,
    customerName: "Fatima Khan",
    eventType: "Engagement",
    date: "2024-01-16",
    time: "4:00 PM - 8:00 PM",
    hall: "Royal Hall B",
    guests: 150,
    status: "pending",
    amount: 80000,
  },
  {
    id: 12,
    customerName: "Hassan Sheikh",
    eventType: "Reception",
    date: "2024-01-17",
    time: "7:00 PM - 12:00 AM",
    hall: "Premium Hall C",
    guests: 250,
    status: "confirmed",
    amount: 120000,
  },
  {
    id: 13,
    customerName: "Zara Ahmed",
    eventType: "Wedding",
    date: "2024-01-20",
    time: "5:00 PM - 10:00 PM",
    hall: "Grand Hall A",
    guests: 400,
    status: "confirmed",
    amount: 200000,
  },
  {
    id: 14,
    customerName: "Omar Khan",
    eventType: "Birthday Party",
    date: "2024-01-22",
    time: "3:00 PM - 7:00 PM",
    hall: "Royal Hall B",
    guests: 100,
    status: "pending",
    amount: 50000,
  },
  {
    id: 15,
    customerName: "Future User",
    eventType: "Wedding",
    date: "2025-08-10",
    time: "6:00 PM - 11:00 PM",
    hall: "Grand Hall A",
    guests: 250,
    status: "confirmed",
    amount: 120000,
  },
  {
    id: 16,
    customerName: "Next Month User",
    eventType: "Birthday",
    date: "2025-09-15",
    time: "3:00 PM - 7:00 PM",
    hall: "Royal Hall B",
    guests: 80,
    status: "pending",
    amount: 40000,
  },
]
