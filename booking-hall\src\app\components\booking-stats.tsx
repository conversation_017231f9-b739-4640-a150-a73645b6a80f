"use client"

import { Calendar, Star, DollarSign } from "lucide-react"

export function BookingStats() {
  // Define border colors similar to calendar
  const borderColors = [
    { default: "#add8e6", hover: "#87ceeb" }, // light blue
    { default: "#ffb6c1", hover: "#ff69b4" }, // light pink
    { default: "#90EE90", hover: "#32CD32" }, // light green
  ]

  return (
    <div className="grid grid-cols-1 gap-4 md:grid-cols-3 mb-6">
      {/* Upcoming Booking */}
      <div
        className="rounded-lg border-2 bg-white p-6 shadow-sm hover:shadow-md transition-all duration-200"
        style={{ borderColor: borderColors[0].default }}
        onMouseEnter={(e) => {
          e.currentTarget.style.borderColor = borderColors[0].hover
        }}
        onMouseLeave={(e) => {
          e.currentTarget.style.borderColor = borderColors[0].default
        }}
      >
        <div className="flex items-center justify-between">
          <h2 className="font-medium text-gray-900">Upcomming Booking</h2>
          <Calendar className="h-5 w-5 text-blue-400" />
        </div>
        <p className="mt-2 text-2xl font-bold text-gray-900">7</p>
        <p className="text-sm text-gray-500">+1 from yesterday</p>
      </div>

      {/* Feedback Highlights */}
      <div
        className="rounded-lg border-2 bg-white p-6 shadow-sm hover:shadow-md transition-all duration-200"
        style={{ borderColor: borderColors[1].default }}
        onMouseEnter={(e) => {
          e.currentTarget.style.borderColor = borderColors[1].hover
        }}
        onMouseLeave={(e) => {
          e.currentTarget.style.borderColor = borderColors[1].default
        }}
      >
        <div className="flex items-center justify-between">
          <h2 className="font-medium text-gray-900">Feedback Highlights</h2>
          <Star className="h-5 w-5 text-pink-400" />
        </div>
        <p className="mt-2 text-2xl font-bold text-gray-900">95%</p>
        <p className="text-sm text-gray-500">customers satisfied</p>
        <p className="text-sm text-gray-500">📣 10 new reviews this month</p>
      </div>

      {/* Financial Summary */}
      <div
        className="rounded-lg border-2 bg-white p-6 shadow-sm hover:shadow-md transition-all duration-200"
        style={{ borderColor: borderColors[2].default }}
        onMouseEnter={(e) => {
          e.currentTarget.style.borderColor = borderColors[2].hover
        }}
        onMouseLeave={(e) => {
          e.currentTarget.style.borderColor = borderColors[2].default
        }}
      >
        <div className="flex items-center justify-between">
          <h2 className="font-medium text-gray-900">💰 Financial Summary</h2>
          <DollarSign className="h-5 w-5 text-green-400" />
        </div>
        <p className="mt-2 text-2xl font-bold text-gray-900">₹5,48,000</p>
        <p className="text-sm text-gray-500">+12% Growth in July</p>
      </div>
    </div>
  )
}
