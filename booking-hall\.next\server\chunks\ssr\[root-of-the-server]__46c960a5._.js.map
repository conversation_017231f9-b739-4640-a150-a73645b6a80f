{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/booking-management/booking-hall/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 55, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/booking-management/booking-hall/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n        outline:\n          \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n        secondary:\n          \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-10 px-4 py-2\",\n        sm: \"h-9 rounded-md px-3\",\n        lg: \"h-11 rounded-md px-8\",\n        icon: \"h-10 w-10\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    const Comp = asChild ? Slot : \"button\"\n    return (\n      <Comp\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AAEA;;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,0RACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC5B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAC9B,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 115, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/booking-management/booking-hall/src/app/components/sidebar.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport { useState } from \"react\"\r\nimport Link from \"next/link\"\r\nimport { usePathname } from \"next/navigation\"\r\nimport { Calendar, LayoutDashboard, Users, Settings, ChevronLeft, ChevronRight } from \"lucide-react\"\r\nimport { Button } from \"@/components/ui/button\"\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst navigation = [\r\n  { name: \"Dashboard\", href: \"/dashboard\", icon: LayoutDashboard },\r\n  { name: \"Booking\", href: \"/booking\", icon: Calendar },\r\n  { name: \"Customer\", href: \"/customer\", icon: Users },\r\n  { name: \"Setting\", href: \"/setting\", icon: Settings },\r\n]\r\n\r\nexport function Sidebar() {\r\n  const [collapsed, setCollapsed] = useState(false)\r\n  const pathname = usePathname()\r\n\r\n  return (\r\n    <div\r\n      className={cn(\r\n        \"flex flex-col h-screen bg-white border-r border-gray-200 transition-all duration-300\",\r\n        collapsed ? \"w-16\" : \"w-64\",\r\n      )}\r\n    >\r\n      {/* Header */}\r\n      <div className=\"flex items-center justify-between p-4 border-b border-gray-200\">\r\n        {!collapsed && (\r\n          <div className=\"flex items-center gap-2\">\r\n            <Calendar className=\"h-6 w-6 text-blue-600\" />\r\n            <span className=\"font-semibold text-gray-900\">Booking System</span>\r\n          </div>\r\n        )}\r\n        <Button variant=\"ghost\" size=\"sm\" onClick={() => setCollapsed(!collapsed)} className=\"h-8 w-8 p-0\">\r\n          {collapsed ? <ChevronRight className=\"h-4 w-4\" /> : <ChevronLeft className=\"h-4 w-4\" />}\r\n        </Button>\r\n      </div>\r\n\r\n      {/* Navigation */}\r\n      <nav className=\"flex-1 p-4\">\r\n        <ul className=\"space-y-2\">\r\n          {navigation.map((item) => {\r\n            const isActive = pathname === item.href\r\n            return (\r\n              <li key={item.name}>\r\n                <Link\r\n                  href={item.href}\r\n                  className={cn(\r\n                    \"flex items-center gap-3 px-3 py-2 rounded-lg text-sm font-medium transition-colors\",\r\n                    isActive ? \"bg-blue-100 text-blue-700\" : \"text-gray-600 hover:bg-gray-100 hover:text-gray-900\",\r\n                  )}\r\n                >\r\n                  <item.icon className=\"h-5 w-5 flex-shrink-0\" />\r\n                  {!collapsed && <span>{item.name}</span>}\r\n                </Link>\r\n              </li>\r\n            )\r\n          })}\r\n        </ul>\r\n      </nav>\r\n    </div>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AAPA;;;;;;;;AASA,MAAM,aAAa;IACjB;QAAE,MAAM;QAAa,MAAM;QAAc,MAAM,4NAAA,CAAA,kBAAe;IAAC;IAC/D;QAAE,MAAM;QAAW,MAAM;QAAY,MAAM,0MAAA,CAAA,WAAQ;IAAC;IACpD;QAAE,MAAM;QAAY,MAAM;QAAa,MAAM,oMAAA,CAAA,QAAK;IAAC;IACnD;QAAE,MAAM;QAAW,MAAM;QAAY,MAAM,0MAAA,CAAA,WAAQ;IAAC;CACrD;AAEM,SAAS;IACd,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAE3B,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wFACA,YAAY,SAAS;;0BAIvB,8OAAC;gBAAI,WAAU;;oBACZ,CAAC,2BACA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,0MAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;0CACpB,8OAAC;gCAAK,WAAU;0CAA8B;;;;;;;;;;;;kCAGlD,8OAAC,kIAAA,CAAA,SAAM;wBAAC,SAAQ;wBAAQ,MAAK;wBAAK,SAAS,IAAM,aAAa,CAAC;wBAAY,WAAU;kCAClF,0BAAY,8OAAC,sNAAA,CAAA,eAAY;4BAAC,WAAU;;;;;iDAAe,8OAAC,oNAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;;;;;;;;;;;;0BAK/E,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAG,WAAU;8BACX,WAAW,GAAG,CAAC,CAAC;wBACf,MAAM,WAAW,aAAa,KAAK,IAAI;wBACvC,qBACE,8OAAC;sCACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAM,KAAK,IAAI;gCACf,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sFACA,WAAW,8BAA8B;;kDAG3C,8OAAC,KAAK,IAAI;wCAAC,WAAU;;;;;;oCACpB,CAAC,2BAAa,8OAAC;kDAAM,KAAK,IAAI;;;;;;;;;;;;2BAT1B,KAAK,IAAI;;;;;oBAatB;;;;;;;;;;;;;;;;;AAKV", "debugId": null}}]}