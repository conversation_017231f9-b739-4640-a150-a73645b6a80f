{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/booking-management/booking-hall/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-lg border bg-card text-card-foreground shadow-sm\",\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\n    {...props}\n  />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      \"text-2xl font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center p-6 pt-0\", className)}\n    {...props}\n  />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,4DACA;QAED,GAAG,KAAK;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 88, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/booking-management/booking-hall/src/app/components/booking-stats.tsx"], "sourcesContent": ["\"use client\"\n\nimport { Calendar, DollarSign, Users, Clock } from \"lucide-react\"\nimport { Card, CardContent } from \"@/components/ui/card\"\n\nexport function BookingStats() {\n  return (\n    <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6\">\n      <Card>\n        <CardContent className=\"p-6\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <p className=\"text-sm font-medium text-gray-600\">Total Bookings</p>\n              <p className=\"text-3xl font-bold text-gray-900\">24</p>\n              <p className=\"text-sm text-green-600\">+12% from last month</p>\n            </div>\n            <div className=\"h-12 w-12 bg-blue-100 rounded-lg flex items-center justify-center\">\n              <Calendar className=\"h-6 w-6 text-blue-600\" />\n            </div>\n          </div>\n        </CardContent>\n      </Card>\n\n      <Card>\n        <CardContent className=\"p-6\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <p className=\"text-sm font-medium text-gray-600\">Revenue</p>\n              <p className=\"text-3xl font-bold text-gray-900\">Rs. 2.4M</p>\n              <p className=\"text-sm text-green-600\">+8% from last month</p>\n            </div>\n            <div className=\"h-12 w-12 bg-green-100 rounded-lg flex items-center justify-center\">\n              <DollarSign className=\"h-6 w-6 text-green-600\" />\n            </div>\n          </div>\n        </CardContent>\n      </Card>\n\n      <Card>\n        <CardContent className=\"p-6\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <p className=\"text-sm font-medium text-gray-600\">Total Guests</p>\n              <p className=\"text-3xl font-bold text-gray-900\">4,200</p>\n              <p className=\"text-sm text-blue-600\">+15% from last month</p>\n            </div>\n            <div className=\"h-12 w-12 bg-purple-100 rounded-lg flex items-center justify-center\">\n              <Users className=\"h-6 w-6 text-purple-600\" />\n            </div>\n          </div>\n        </CardContent>\n      </Card>\n\n      <Card>\n        <CardContent className=\"p-6\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <p className=\"text-sm font-medium text-gray-600\">Pending</p>\n              <p className=\"text-3xl font-bold text-gray-900\">6</p>\n              <p className=\"text-sm text-orange-600\">Awaiting confirmation</p>\n            </div>\n            <div className=\"h-12 w-12 bg-orange-100 rounded-lg flex items-center justify-center\">\n              <Clock className=\"h-6 w-6 text-orange-600\" />\n            </div>\n          </div>\n        </CardContent>\n      </Card>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAAA;AAAA;AACA;AAHA;;;;AAKO,SAAS;IACd,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,gIAAA,CAAA,OAAI;0BACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;oBAAC,WAAU;8BACrB,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;;kDACC,8OAAC;wCAAE,WAAU;kDAAoC;;;;;;kDACjD,8OAAC;wCAAE,WAAU;kDAAmC;;;;;;kDAChD,8OAAC;wCAAE,WAAU;kDAAyB;;;;;;;;;;;;0CAExC,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,0MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAM5B,8OAAC,gIAAA,CAAA,OAAI;0BACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;oBAAC,WAAU;8BACrB,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;;kDACC,8OAAC;wCAAE,WAAU;kDAAoC;;;;;;kDACjD,8OAAC;wCAAE,WAAU;kDAAmC;;;;;;kDAChD,8OAAC;wCAAE,WAAU;kDAAyB;;;;;;;;;;;;0CAExC,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,kNAAA,CAAA,aAAU;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAM9B,8OAAC,gIAAA,CAAA,OAAI;0BACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;oBAAC,WAAU;8BACrB,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;;kDACC,8OAAC;wCAAE,WAAU;kDAAoC;;;;;;kDACjD,8OAAC;wCAAE,WAAU;kDAAmC;;;;;;kDAChD,8OAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;0CAEvC,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAMzB,8OAAC,gIAAA,CAAA,OAAI;0BACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;oBAAC,WAAU;8BACrB,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;;kDACC,8OAAC;wCAAE,WAAU;kDAAoC;;;;;;kDACjD,8OAAC;wCAAE,WAAU;kDAAmC;;;;;;kDAChD,8OAAC;wCAAE,WAAU;kDAA0B;;;;;;;;;;;;0CAEzC,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO/B", "debugId": null}}, {"offset": {"line": 390, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/booking-management/booking-hall/src/app/booking/components/booking-action-bar.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport { Calendar, Plus, CalendarDays } from \"lucide-react\"\r\n\r\ninterface BookingActionBarProps {\r\n  showCalendar: boolean\r\n  setShowCalendar: (show: boolean | ((prev: boolean) => boolean)) => void\r\n  setShowBookingModal: (show: boolean) => void\r\n  setShowAllBookingsModal: (show: boolean) => void\r\n}\r\n\r\nexport function BookingActionBar({\r\n  showCalendar,\r\n  setShowCalendar,\r\n  setShowBookingModal,\r\n  setShowAllBookingsModal,\r\n}: BookingActionBarProps) {\r\n  return (\r\n    <div className=\"flex flex-col sm:flex-row gap-4 mb-6\">\r\n      <div className=\"flex-1 flex gap-2\">\r\n        {/* Calendar button */}\r\n        <button\r\n          onClick={() => setShowCalendar((prev) => !prev)}\r\n          className=\"px-4 py-2 bg-gray-300 rounded-lg hover:bg-green-200 transition-colors flex items-center gap-2\"\r\n        >\r\n          <Calendar className=\"h-4 w-4 text-gray-600\" />\r\n          <span className=\"text-sm text-gray-700\">Calendar</span>\r\n        </button>\r\n      </div>\r\n\r\n      <div className=\"flex gap-2\">\r\n        {/* Booking Form Button */}\r\n        <button\r\n          onClick={() => setShowBookingModal(true)}\r\n          className=\"px-4 py-2 bg-gray-300 text-black rounded-lg hover:bg-green-200 flex items-center gap-2 transition-colors shadow-sm\"\r\n        >\r\n          <Plus className=\"h-4 w-4\" />\r\n          Booking Form\r\n        </button>\r\n\r\n        {/* Booking Data Button */}\r\n        <button\r\n          onClick={() => setShowAllBookingsModal(true)}\r\n          className=\"px-4 py-2 bg-gray-300 text-black rounded-lg hover:bg-green-200 flex items-center gap-2 transition-colors shadow-sm\"\r\n        >\r\n          <CalendarDays className=\"h-4 w-4\" />\r\n          Booking Data\r\n        </button>\r\n      </div>\r\n    </div>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAAA;AAFA;;;AAWO,SAAS,iBAAiB,EAC/B,YAAY,EACZ,eAAe,EACf,mBAAmB,EACnB,uBAAuB,EACD;IACtB,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;0BAEb,cAAA,8OAAC;oBACC,SAAS,IAAM,gBAAgB,CAAC,OAAS,CAAC;oBAC1C,WAAU;;sCAEV,8OAAC,0MAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;sCACpB,8OAAC;4BAAK,WAAU;sCAAwB;;;;;;;;;;;;;;;;;0BAI5C,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBACC,SAAS,IAAM,oBAAoB;wBACnC,WAAU;;0CAEV,8OAAC,kMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;4BAAY;;;;;;;kCAK9B,8OAAC;wBACC,SAAS,IAAM,wBAAwB;wBACvC,WAAU;;0CAEV,8OAAC,sNAAA,CAAA,eAAY;gCAAC,WAAU;;;;;;4BAAY;;;;;;;;;;;;;;;;;;;AAM9C", "debugId": null}}, {"offset": {"line": 494, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/booking-management/booking-hall/src/app/booking/utils/calender-utils.ts"], "sourcesContent": ["export class CalendarUtils {\r\n  // Generate calendar dates for upcoming months (3 months ahead)\r\n  static generateCalendarDates(startMonth = 0) {\r\n    const today = new Date()\r\n    const currentYear = today.getFullYear()\r\n    const currentMonth = today.getMonth()\r\n    const targetDate = new Date(currentYear, currentMonth + startMonth, 1)\r\n    const year = targetDate.getFullYear()\r\n    const month = targetDate.getMonth()\r\n\r\n    // Get first day of month and how many days in month\r\n    const firstDay = new Date(year, month, 1).getDay()\r\n    const daysInMonth = new Date(year, month + 1, 0).getDate()\r\n\r\n    // Generate calendar grid (42 days = 6 weeks)\r\n    const dates = []\r\n\r\n    // Previous month's trailing days\r\n    for (let i = firstDay - 1; i >= 0; i--) {\r\n      const prevDate = new Date(year, month, -i)\r\n      dates.push({\r\n        date: prevDate,\r\n        dateStr: prevDate.toISOString().split(\"T\")[0],\r\n        isCurrentMonth: false,\r\n        isPast: prevDate < today,\r\n      })\r\n    }\r\n\r\n    // Current month's days\r\n    for (let day = 1; day <= daysInMonth; day++) {\r\n      const currentDate = new Date(year, month, day)\r\n      dates.push({\r\n        date: currentDate,\r\n        dateStr: currentDate.toISOString().split(\"T\")[0],\r\n        isCurrentMonth: true,\r\n        isPast: currentDate < today,\r\n      })\r\n    }\r\n\r\n    // Next month's leading days\r\n    const remainingSlots = 42 - dates.length\r\n    for (let day = 1; day <= remainingSlots; day++) {\r\n      const nextDate = new Date(year, month + 1, day)\r\n      dates.push({\r\n        date: nextDate,\r\n        dateStr: nextDate.toISOString().split(\"T\")[0],\r\n        isCurrentMonth: false,\r\n        isPast: false,\r\n      })\r\n    }\r\n\r\n    return { dates, month, year }\r\n  }\r\n\r\n  // Get available time slots for a date\r\n  static getAvailableTimeSlots(dateBookings: any[]) {\r\n    const allSlots = [\r\n      { id: \"morning\", name: \"Morning (9:00 AM - 1:00 PM)\", time: \"9:00 AM - 1:00 PM\" },\r\n      { id: \"afternoon\", name: \"Afternoon (2:00 PM - 6:00 PM)\", time: \"2:00 PM - 6:00 PM\" },\r\n      { id: \"evening\", name: \"Evening (6:00 PM - 11:00 PM)\", time: \"6:00 PM - 11:00 PM\" },\r\n      { id: \"night\", name: \"Night (7:00 PM - 12:00 AM)\", time: \"7:00 PM - 12:00 AM\" },\r\n    ]\r\n\r\n    const bookedSlots: string[] = dateBookings\r\n      .map((booking) => {\r\n        const time = booking.time.toLowerCase()\r\n        if (time.includes(\"morning\") || time.includes(\"9:00\")) return \"morning\"\r\n        if (time.includes(\"afternoon\") || time.includes(\"2:00\")) return \"afternoon\"\r\n        if (time.includes(\"evening\") || time.includes(\"6:00\")) return \"evening\"\r\n        if (time.includes(\"night\") || time.includes(\"7:00\")) return \"night\"\r\n        return \"\"\r\n      })\r\n      .filter((slot) => slot !== \"\")\r\n\r\n    return allSlots.map((slot) => ({\r\n      ...slot,\r\n      isBooked: bookedSlots.includes(slot.id),\r\n      bookingDetails: dateBookings.find((booking) => {\r\n        const time = booking.time.toLowerCase()\r\n        return (\r\n          time.includes(slot.id) ||\r\n          (slot.id === \"morning\" && time.includes(\"9:00\")) ||\r\n          (slot.id === \"afternoon\" && time.includes(\"2:00\")) ||\r\n          (slot.id === \"evening\" && time.includes(\"6:00\")) ||\r\n          (slot.id === \"night\" && time.includes(\"7:00\"))\r\n        )\r\n      }),\r\n    }))\r\n  }\r\n}\r\n\r\n"], "names": [], "mappings": ";;;AAAO,MAAM;IACX,+DAA+D;IAC/D,OAAO,sBAAsB,aAAa,CAAC,EAAE;QAC3C,MAAM,QAAQ,IAAI;QAClB,MAAM,cAAc,MAAM,WAAW;QACrC,MAAM,eAAe,MAAM,QAAQ;QACnC,MAAM,aAAa,IAAI,KAAK,aAAa,eAAe,YAAY;QACpE,MAAM,OAAO,WAAW,WAAW;QACnC,MAAM,QAAQ,WAAW,QAAQ;QAEjC,oDAAoD;QACpD,MAAM,WAAW,IAAI,KAAK,MAAM,OAAO,GAAG,MAAM;QAChD,MAAM,cAAc,IAAI,KAAK,MAAM,QAAQ,GAAG,GAAG,OAAO;QAExD,6CAA6C;QAC7C,MAAM,QAAQ,EAAE;QAEhB,iCAAiC;QACjC,IAAK,IAAI,IAAI,WAAW,GAAG,KAAK,GAAG,IAAK;YACtC,MAAM,WAAW,IAAI,KAAK,MAAM,OAAO,CAAC;YACxC,MAAM,IAAI,CAAC;gBACT,MAAM;gBACN,SAAS,SAAS,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;gBAC7C,gBAAgB;gBAChB,QAAQ,WAAW;YACrB;QACF;QAEA,uBAAuB;QACvB,IAAK,IAAI,MAAM,GAAG,OAAO,aAAa,MAAO;YAC3C,MAAM,cAAc,IAAI,KAAK,MAAM,OAAO;YAC1C,MAAM,IAAI,CAAC;gBACT,MAAM;gBACN,SAAS,YAAY,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;gBAChD,gBAAgB;gBAChB,QAAQ,cAAc;YACxB;QACF;QAEA,4BAA4B;QAC5B,MAAM,iBAAiB,KAAK,MAAM,MAAM;QACxC,IAAK,IAAI,MAAM,GAAG,OAAO,gBAAgB,MAAO;YAC9C,MAAM,WAAW,IAAI,KAAK,MAAM,QAAQ,GAAG;YAC3C,MAAM,IAAI,CAAC;gBACT,MAAM;gBACN,SAAS,SAAS,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;gBAC7C,gBAAgB;gBAChB,QAAQ;YACV;QACF;QAEA,OAAO;YAAE;YAAO;YAAO;QAAK;IAC9B;IAEA,sCAAsC;IACtC,OAAO,sBAAsB,YAAmB,EAAE;QAChD,MAAM,WAAW;YACf;gBAAE,IAAI;gBAAW,MAAM;gBAA+B,MAAM;YAAoB;YAChF;gBAAE,IAAI;gBAAa,MAAM;gBAAiC,MAAM;YAAoB;YACpF;gBAAE,IAAI;gBAAW,MAAM;gBAAgC,MAAM;YAAqB;YAClF;gBAAE,IAAI;gBAAS,MAAM;gBAA8B,MAAM;YAAqB;SAC/E;QAED,MAAM,cAAwB,aAC3B,GAAG,CAAC,CAAC;YACJ,MAAM,OAAO,QAAQ,IAAI,CAAC,WAAW;YACrC,IAAI,KAAK,QAAQ,CAAC,cAAc,KAAK,QAAQ,CAAC,SAAS,OAAO;YAC9D,IAAI,KAAK,QAAQ,CAAC,gBAAgB,KAAK,QAAQ,CAAC,SAAS,OAAO;YAChE,IAAI,KAAK,QAAQ,CAAC,cAAc,KAAK,QAAQ,CAAC,SAAS,OAAO;YAC9D,IAAI,KAAK,QAAQ,CAAC,YAAY,KAAK,QAAQ,CAAC,SAAS,OAAO;YAC5D,OAAO;QACT,GACC,MAAM,CAAC,CAAC,OAAS,SAAS;QAE7B,OAAO,SAAS,GAAG,CAAC,CAAC,OAAS,CAAC;gBAC7B,GAAG,IAAI;gBACP,UAAU,YAAY,QAAQ,CAAC,KAAK,EAAE;gBACtC,gBAAgB,aAAa,IAAI,CAAC,CAAC;oBACjC,MAAM,OAAO,QAAQ,IAAI,CAAC,WAAW;oBACrC,OACE,KAAK,QAAQ,CAAC,KAAK,EAAE,KACpB,KAAK,EAAE,KAAK,aAAa,KAAK,QAAQ,CAAC,WACvC,KAAK,EAAE,KAAK,eAAe,KAAK,QAAQ,CAAC,WACzC,KAAK,EAAE,KAAK,aAAa,KAAK,QAAQ,CAAC,WACvC,KAAK,EAAE,KAAK,WAAW,KAAK,QAAQ,CAAC;gBAE1C;YACF,CAAC;IACH;AACF", "debugId": null}}, {"offset": {"line": 596, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/booking-management/booking-hall/src/app/booking/components/booking%20calender.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport { useState } from \"react\"\r\nimport { CalendarUtils } from \"../utils/calender-utils\"\r\n\r\ninterface BookingCalendarProps {\r\n  currentCalendarMonth: number\r\n  setCurrentCalendarMonth: (month: number | ((prev: number) => number)) => void\r\n  selectedDate: string\r\n  setSelectedCalendarDate: (date: string | null) => void\r\n  bookings: any[]\r\n}\r\n\r\nexport function BookingCalendar({\r\n  currentCalendarMonth,\r\n  setCurrentCalendarMonth,\r\n  selectedDate,\r\n  setSelectedCalendarDate,\r\n  bookings,\r\n}: BookingCalendarProps) {\r\n  const [clickedBookedDate, setClickedBookedDate] = useState<string | null>(null)\r\n\r\n  // Define border colors for each calendar\r\n  const calendarBorders = [\r\n    {\r\n      default: \"#add8e6\", // light blue\r\n      hover: \"#87ceeb\", // darker blue\r\n    },\r\n    {\r\n      default: \"#ffb6c1\", // light pink\r\n      hover: \"#ff69b4\", // darker pink\r\n    },\r\n    {\r\n     default: \"#d0f0c0\", // light green\r\nhover: \"#a3d977\",   // darker green\r\n    },\r\n  ]\r\n\r\n  return (\r\n    <div className=\"bg-white rounded-lg border border-gray-200 p-6 mb-6\">\r\n      {/* Month Navigation */}\r\n      <div className=\"flex items-center justify-between mb-4\">\r\n        <button\r\n          onClick={() => setCurrentCalendarMonth((prev) => prev - 1)}\r\n          className=\"px-3 py-1 rounded bg-gray-100 hover:bg-gray-200 text-gray-700\"\r\n        >\r\n          Previous\r\n        </button>\r\n        <div className=\"text-xl font-bold text-gray-800\">\r\n          {(() => {\r\n            const today = new Date()\r\n            const startMonth = new Date(today.getFullYear(), today.getMonth() + currentCalendarMonth, 1)\r\n            const endMonth = new Date(today.getFullYear(), today.getMonth() + currentCalendarMonth + 2, 1)\r\n            return `${startMonth.toLocaleDateString(\"en-US\", { month: \"long\", year: \"numeric\" })} - ${endMonth.toLocaleDateString(\"en-US\", { month: \"long\", year: \"numeric\" })}`\r\n          })()}\r\n        </div>\r\n        <button\r\n          onClick={() => setCurrentCalendarMonth((prev) => prev + 1)}\r\n          className=\"px-3 py-1 rounded bg-gray-100 hover:bg-gray-200 text-gray-700\"\r\n        >\r\n          Next\r\n        </button>\r\n      </div>\r\n\r\n      {/* 3 Months Calendar Grid */}\r\n      <div className=\"flex flex-col md:flex-row gap-6\">\r\n        {[0, 1, 2].map((offset) => {\r\n          const { dates, month, year } = CalendarUtils.generateCalendarDates(currentCalendarMonth + offset)\r\n          const borderColors = calendarBorders[offset]\r\n\r\n          return (\r\n            <div\r\n              key={offset}\r\n              className=\"flex-1 min-w-[220px] bg-white rounded-lg shadow p-3 transition-all duration-200 group\"\r\n              style={{\r\n                border: `2px solid ${borderColors.default}`,\r\n              }}\r\n              onMouseEnter={(e) => {\r\n                e.currentTarget.style.borderColor = borderColors.hover\r\n              }}\r\n              onMouseLeave={(e) => {\r\n                e.currentTarget.style.borderColor = borderColors.default\r\n              }}\r\n            >\r\n              <div className=\"text-center font-bold bg-gray-200 text-gray-800 mb-2 py-2 rounded\">\r\n                {new Date(year, month, 1).toLocaleDateString(\"en-US\", { month: \"long\", year: \"numeric\" })}\r\n              </div>\r\n              <div className=\"grid grid-cols-7 gap-2 mb-2\">\r\n                {[\"Sun\", \"Mon\", \"Tue\", \"Wed\", \"Thu\", \"Fri\", \"Sat\"].map((day) => (\r\n                  <div key={day} className=\"text-center text-xs font-medium text-gray-500 py-1\">\r\n                    {day}\r\n                  </div>\r\n                ))}\r\n              </div>\r\n              <div className=\"grid grid-cols-7 gap-2\">\r\n                {dates.map((dateInfo, i) => {\r\n                  const hasBooking = bookings.some((booking) => booking.date === dateInfo.dateStr)\r\n                  const isClicked = clickedBookedDate === dateInfo.dateStr\r\n                  const isToday = new Date().toDateString() === dateInfo.date.toDateString()\r\n\r\n                  return (\r\n                    <div key={i} className=\"relative\">\r\n                      <button\r\n                        type=\"button\"\r\n                        onClick={() => {\r\n                          if (hasBooking) {\r\n                            // If clicking the same date, toggle it off, otherwise set new date\r\n                            setClickedBookedDate(isClicked ? null : dateInfo.dateStr)\r\n                          } else {\r\n                            // If clicking non-booked date, clear selection\r\n                            setClickedBookedDate(null)\r\n                          }\r\n                        }}\r\n                        disabled={dateInfo.isPast}\r\n                        className={`w-full p-2 text-xs rounded-lg border transition-colors relative flex flex-col items-center justify-center min-h-[32px]\r\n                          ${\r\n                            !dateInfo.isCurrentMonth\r\n                              ? \"text-gray-400 border-gray-200\"\r\n                              : dateInfo.isPast\r\n                                ? \"text-gray-300 cursor-not-allowed border-gray-200\"\r\n                                : isClicked\r\n                                  ? \"bg-orange-200 text-orange-900 border-orange-300\"\r\n                                  : isToday\r\n                                    ? \"bg-orange-100 text-orange-800 border-orange-300\"\r\n                                    : hasBooking\r\n                                      ? \"bg-green-100 text-green-800 border-green-300 hover:bg-green-200 cursor-pointer\"\r\n                                      : \"hover:bg-gray-50 text-gray-700 border-gray-300 cursor-pointer\"\r\n                          }\r\n                        `}\r\n                      >\r\n                        {dateInfo.date.getDate()}\r\n                        {/* Booking indicator dots */}\r\n                        {hasBooking && dateInfo.isCurrentMonth && (\r\n                          <span className=\"absolute bottom-1 right-1 flex gap-0.5\">\r\n                            <span className=\"w-1 h-1 bg-gray-600 rounded-full\"></span>\r\n                            <span className=\"w-1 h-1 bg-gray-600 rounded-full\"></span>\r\n                            <span className=\"w-1 h-1 bg-gray-600 rounded-full\"></span>\r\n                          </span>\r\n                        )}\r\n                      </button>\r\n                      {/* Inline \"Booked\" text under clicked date */}\r\n                      {isClicked && hasBooking && (\r\n                        <div className=\"absolute top-full left-1/2 transform -translate-x-1/2 mt-1 z-10\">\r\n                          <span className=\"text-xs bg-orange-200 text-orange-800 px-2 py-1 rounded shadow-sm whitespace-nowrap\">\r\n                            Booked\r\n                          </span>\r\n                        </div>\r\n                      )}\r\n                    </div>\r\n                  )\r\n                })}\r\n              </div>\r\n            </div>\r\n          )\r\n        })}\r\n      </div>\r\n    </div>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAaO,SAAS,gBAAgB,EAC9B,oBAAoB,EACpB,uBAAuB,EACvB,YAAY,EACZ,uBAAuB,EACvB,QAAQ,EACa;IACrB,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAE1E,yCAAyC;IACzC,MAAM,kBAAkB;QACtB;YACE,SAAS;YACT,OAAO;QACT;QACA;YACE,SAAS;YACT,OAAO;QACT;QACA;YACC,SAAS;YACd,OAAO;QACH;KACD;IAED,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBACC,SAAS,IAAM,wBAAwB,CAAC,OAAS,OAAO;wBACxD,WAAU;kCACX;;;;;;kCAGD,8OAAC;wBAAI,WAAU;kCACZ,CAAC;4BACA,MAAM,QAAQ,IAAI;4BAClB,MAAM,aAAa,IAAI,KAAK,MAAM,WAAW,IAAI,MAAM,QAAQ,KAAK,sBAAsB;4BAC1F,MAAM,WAAW,IAAI,KAAK,MAAM,WAAW,IAAI,MAAM,QAAQ,KAAK,uBAAuB,GAAG;4BAC5F,OAAO,GAAG,WAAW,kBAAkB,CAAC,SAAS;gCAAE,OAAO;gCAAQ,MAAM;4BAAU,GAAG,GAAG,EAAE,SAAS,kBAAkB,CAAC,SAAS;gCAAE,OAAO;gCAAQ,MAAM;4BAAU,IAAI;wBACtK,CAAC;;;;;;kCAEH,8OAAC;wBACC,SAAS,IAAM,wBAAwB,CAAC,OAAS,OAAO;wBACxD,WAAU;kCACX;;;;;;;;;;;;0BAMH,8OAAC;gBAAI,WAAU;0BACZ;oBAAC;oBAAG;oBAAG;iBAAE,CAAC,GAAG,CAAC,CAAC;oBACd,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,mJAAA,CAAA,gBAAa,CAAC,qBAAqB,CAAC,uBAAuB;oBAC1F,MAAM,eAAe,eAAe,CAAC,OAAO;oBAE5C,qBACE,8OAAC;wBAEC,WAAU;wBACV,OAAO;4BACL,QAAQ,CAAC,UAAU,EAAE,aAAa,OAAO,EAAE;wBAC7C;wBACA,cAAc,CAAC;4BACb,EAAE,aAAa,CAAC,KAAK,CAAC,WAAW,GAAG,aAAa,KAAK;wBACxD;wBACA,cAAc,CAAC;4BACb,EAAE,aAAa,CAAC,KAAK,CAAC,WAAW,GAAG,aAAa,OAAO;wBAC1D;;0CAEA,8OAAC;gCAAI,WAAU;0CACZ,IAAI,KAAK,MAAM,OAAO,GAAG,kBAAkB,CAAC,SAAS;oCAAE,OAAO;oCAAQ,MAAM;gCAAU;;;;;;0CAEzF,8OAAC;gCAAI,WAAU;0CACZ;oCAAC;oCAAO;oCAAO;oCAAO;oCAAO;oCAAO;oCAAO;iCAAM,CAAC,GAAG,CAAC,CAAC,oBACtD,8OAAC;wCAAc,WAAU;kDACtB;uCADO;;;;;;;;;;0CAKd,8OAAC;gCAAI,WAAU;0CACZ,MAAM,GAAG,CAAC,CAAC,UAAU;oCACpB,MAAM,aAAa,SAAS,IAAI,CAAC,CAAC,UAAY,QAAQ,IAAI,KAAK,SAAS,OAAO;oCAC/E,MAAM,YAAY,sBAAsB,SAAS,OAAO;oCACxD,MAAM,UAAU,IAAI,OAAO,YAAY,OAAO,SAAS,IAAI,CAAC,YAAY;oCAExE,qBACE,8OAAC;wCAAY,WAAU;;0DACrB,8OAAC;gDACC,MAAK;gDACL,SAAS;oDACP,IAAI,YAAY;wDACd,mEAAmE;wDACnE,qBAAqB,YAAY,OAAO,SAAS,OAAO;oDAC1D,OAAO;wDACL,+CAA+C;wDAC/C,qBAAqB;oDACvB;gDACF;gDACA,UAAU,SAAS,MAAM;gDACzB,WAAW,CAAC;0BACV,EACE,CAAC,SAAS,cAAc,GACpB,kCACA,SAAS,MAAM,GACb,qDACA,YACE,oDACA,UACE,oDACA,aACE,mFACA,gEACb;wBACH,CAAC;;oDAEA,SAAS,IAAI,CAAC,OAAO;oDAErB,cAAc,SAAS,cAAc,kBACpC,8OAAC;wDAAK,WAAU;;0EACd,8OAAC;gEAAK,WAAU;;;;;;0EAChB,8OAAC;gEAAK,WAAU;;;;;;0EAChB,8OAAC;gEAAK,WAAU;;;;;;;;;;;;;;;;;;4CAKrB,aAAa,4BACZ,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAK,WAAU;8DAAsF;;;;;;;;;;;;uCA1ClG;;;;;gCAiDd;;;;;;;uBA9EG;;;;;gBAkFX;;;;;;;;;;;;AAIR", "debugId": null}}, {"offset": {"line": 841, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/booking-management/booking-hall/src/app/booking/components/booking-modal.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport type React from \"react\"\r\n\r\nimport { useState } from \"react\"\r\n\r\nimport { User, <PERSON>, Setting<PERSON>, Star } from \"lucide-react\"\r\n\r\ninterface BookingModalProps {\r\n  setShowBookingModal: (show: boolean) => void\r\n  customerName: string\r\n  setCustomerName: (name: string) => void\r\n  customerPhone: string\r\n  setCustomerPhone: (phone: string) => void\r\n  customerEmail: string\r\n  setCustomerEmail: (email: string) => void\r\n  eventType: string\r\n  setEventType: (type: string) => void\r\n  guestCount: string\r\n  setGuestCount: (count: string) => void\r\n  selectedHall: string\r\n  setSelectedHall: (hall: string) => void\r\n  specialRequests: string\r\n  setSpecialRequests: (requests: string) => void\r\n  newBookingDate: string\r\n  setNewBookingDate: (date: string) => void\r\n  newBookingTime: string\r\n  setNewBookingTime: (time: string) => void\r\n  eventCategories: any[]\r\n}\r\n\r\nexport function BookingModal(props: BookingModalProps) {\r\n  const {\r\n    setShowBookingModal,\r\n    customerName,\r\n    setCustomerName,\r\n    customerPhone,\r\n    setCustomerP<PERSON>,\r\n    customerEmail,\r\n    setCustomerEmail,\r\n    eventType,\r\n    setEventType,\r\n    guestCount,\r\n    setGuestCount,\r\n    selectedHall,\r\n    setSelectedHall,\r\n    specialRequests,\r\n    setSpecialRequests,\r\n    newBookingDate,\r\n    setNewBookingDate,\r\n    newBookingTime,\r\n    setNewBookingTime,\r\n    eventCategories,\r\n  } = props\r\n\r\n  // Additional state for new form fields\r\n  const [nicNumber, setNicNumber] = useState(\"\")\r\n  const [entryType, setEntryType] = useState(\"\")\r\n  const [decorationRequirements, setDecorationRequirements] = useState(\"\")\r\n  const [receptionType, setReceptionType] = useState(\"\")\r\n  const [maleFemaleSepa, setMaleFemaleSepa] = useState(false)\r\n  const [airConditioning, setAirConditioning] = useState(false)\r\n  const [maleFemaleSt, setMaleFemaleSt] = useState(false)\r\n\r\n  const handleSubmit = (e: React.FormEvent) => {\r\n    e.preventDefault()\r\n    console.log(\"Form submitted with all data\")\r\n    setShowBookingModal(false)\r\n  }\r\n\r\n  return (\r\n    <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\">\r\n      <div className=\"bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto\">\r\n        {/* Header */}\r\n        <div className=\"flex items-center justify-between p-6 border-b bg-gradient-to-r from-blue-50 to-purple-50\">\r\n          <div>\r\n            <h2 className=\"text-2xl font-bold text-gray-900\">Marriage Hall Booking Form</h2>\r\n            <p className=\"text-sm text-gray-600 mt-1\">Please fill out all sections to complete your booking</p>\r\n          </div>\r\n          <button\r\n            onClick={() => setShowBookingModal(false)}\r\n            className=\"text-gray-400 hover:text-gray-600 transition-colors\"\r\n          >\r\n            <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\r\n            </svg>\r\n          </button>\r\n        </div>\r\n\r\n        {/* Form Content */}\r\n        <form onSubmit={handleSubmit} className=\"p-6\">\r\n          <div className=\"space-y-6\">\r\n            {/* Personal Information Section */}\r\n            <div className=\"space-y-3\">\r\n              {/* Heading Box */}\r\n              <div\r\n                className=\"border-2 rounded-md p-3 shadow-md bg-gradient-to-r from-blue-50 to-blue-100 transition-all duration-200 group\"\r\n                style={{ borderColor: \"#add8e6\" }}\r\n                onMouseEnter={(e) => {\r\n                  e.currentTarget.style.borderColor = \"#87ceeb\"\r\n                }}\r\n                onMouseLeave={(e) => {\r\n                  e.currentTarget.style.borderColor = \"#add8e6\"\r\n                }}\r\n              >\r\n                <div className=\"flex items-center gap-2\">\r\n                  <User className=\"h-5 w-5 text-blue-600\" />\r\n                  <h3 className=\"text-lg font-semibold text-gray-900\">Personal Information</h3>\r\n                </div>\r\n              </div>\r\n              {/* Content Box */}\r\n              <div className=\"border border-gray-200 rounded-md p-4 shadow-md bg-white\">\r\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">Full Name *</label>\r\n                    <input\r\n                      type=\"text\"\r\n                      value={customerName}\r\n                      onChange={(e) => setCustomerName(e.target.value)}\r\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\r\n                      placeholder=\"Enter your full name\"\r\n                      required\r\n                    />\r\n                  </div>\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">NIC Number *</label>\r\n                    <input\r\n                      type=\"text\"\r\n                      value={nicNumber}\r\n                      onChange={(e) => setNicNumber(e.target.value)}\r\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\r\n                      placeholder=\"Enter your NIC number\"\r\n                      required\r\n                    />\r\n                  </div>\r\n                  <div className=\"md:col-span-2\">\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">Contact Number *</label>\r\n                    <input\r\n                      type=\"tel\"\r\n                      value={customerPhone}\r\n                      onChange={(e) => setCustomerPhone(e.target.value)}\r\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\r\n                      placeholder=\"Enter your contact number\"\r\n                      required\r\n                    />\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            {/* About Event Section */}\r\n            <div className=\"space-y-3\">\r\n              {/* Heading Box */}\r\n              <div\r\n                className=\"border-2 rounded-md p-3 shadow-md bg-gradient-to-r from-pink-50 to-pink-100 transition-all duration-200 group\"\r\n                style={{ borderColor: \"#ffb6c1\" }}\r\n                onMouseEnter={(e) => {\r\n                  e.currentTarget.style.borderColor = \"#ff69b4\"\r\n                }}\r\n                onMouseLeave={(e) => {\r\n                  e.currentTarget.style.borderColor = \"#ffb6c1\"\r\n                }}\r\n              >\r\n                <div className=\"flex items-center gap-2\">\r\n                  <Calendar className=\"h-5 w-5 text-pink-600\" />\r\n                  <h3 className=\"text-lg font-semibold text-gray-900\">About Event</h3>\r\n                </div>\r\n              </div>\r\n              {/* Content Box */}\r\n              <div className=\"border border-gray-200 rounded-md p-4 shadow-md bg-white\">\r\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">Event Type *</label>\r\n                    <select\r\n                      value={eventType}\r\n                      onChange={(e) => setEventType(e.target.value)}\r\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-pink-500\"\r\n                      required\r\n                    >\r\n                      <option value=\"\">Select Event Type</option>\r\n                      <option value=\"Wedding\">Wedding</option>\r\n                      <option value=\"Engagement\">Engagement</option>\r\n                      <option value=\"Mehndi\">Mehndi</option>\r\n                      <option value=\"Birthday\">Birthday</option>\r\n                      <option value=\"Valima\">Valima</option>\r\n                      <option value=\"Melad\">Melad</option>\r\n                      <option value=\"Other\">Other</option>\r\n                    </select>\r\n                  </div>\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">Event Date *</label>\r\n                    <div className=\"relative\">\r\n                      <input\r\n                        type=\"date\"\r\n                        value={newBookingDate}\r\n                        onChange={(e) => setNewBookingDate(e.target.value)}\r\n                        className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-pink-500\"\r\n                        required\r\n                      />\r\n                      <Calendar className=\"absolute right-3 top-2.5 h-4 w-4 text-gray-400 pointer-events-none\" />\r\n                    </div>\r\n                  </div>\r\n                  <div className=\"md:col-span-2\">\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">Number of Guests *</label>\r\n                    <input\r\n                      type=\"number\"\r\n                      value={guestCount}\r\n                      onChange={(e) => setGuestCount(e.target.value)}\r\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-pink-500\"\r\n                      placeholder=\"Enter expected number of guests\"\r\n                      min=\"1\"\r\n                      required\r\n                    />\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            {/* Requirements Section */}\r\n            <div className=\"space-y-3\">\r\n              {/* Heading Box */}\r\n              <div\r\n                className=\"border-2 rounded-md p-3 shadow-md bg-gradient-to-r from-yellow-50 to-yellow-100 transition-all duration-200 group\"\r\n                style={{ borderColor: \"#ffffe0\" }}\r\n                onMouseEnter={(e) => {\r\n                  e.currentTarget.style.borderColor = \"#fdfd96\"\r\n                }}\r\n                onMouseLeave={(e) => {\r\n                  e.currentTarget.style.borderColor = \"#ffffe0\"\r\n                }}\r\n              >\r\n                <div className=\"flex items-center gap-2\">\r\n                  <Settings className=\"h-5 w-5 text-yellow-600\" />\r\n                  <h3 className=\"text-lg font-semibold text-gray-900\">Requirements</h3>\r\n                </div>\r\n              </div>\r\n              {/* Content Box */}\r\n              <div className=\"border border-gray-200 rounded-md p-4 shadow-md bg-white\">\r\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">Entry Type *</label>\r\n                    <select\r\n                      value={entryType}\r\n                      onChange={(e) => setEntryType(e.target.value)}\r\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500\"\r\n                      required\r\n                    >\r\n                      <option value=\"\">Select Entry Type</option>\r\n                      <option value=\"Entry with Baghi\">Entry with Baghi</option>\r\n                      <option value=\"Entry with Flower Doli\">Entry with Flower Doli</option>\r\n                    </select>\r\n                  </div>\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">Decoration Requirements *</label>\r\n                    <select\r\n                      value={decorationRequirements}\r\n                      onChange={(e) => setDecorationRequirements(e.target.value)}\r\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500\"\r\n                      required\r\n                    >\r\n                      <option value=\"\">Select Decoration Type</option>\r\n                      <option value=\"Original Flower\">Original Flower</option>\r\n                      <option value=\"Imitation Flower\">Imitation Flower</option>\r\n                    </select>\r\n                  </div>\r\n                  <div className=\"md:col-span-2\">\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">Type of Reception *</label>\r\n                    <select\r\n                      value={receptionType}\r\n                      onChange={(e) => setReceptionType(e.target.value)}\r\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500\"\r\n                      required\r\n                    >\r\n                      <option value=\"\">Select Reception Type</option>\r\n                      <option value=\"Morning\">Morning</option>\r\n                      <option value=\"Day\">Day</option>\r\n                      <option value=\"Evening\">Evening</option>\r\n                      <option value=\"Night\">Night</option>\r\n                    </select>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            {/* Highly Recommended Section */}\r\n            <div className=\"space-y-3\">\r\n              {/* Heading Box */}\r\n              <div\r\n                className=\"border-2 rounded-md p-3 shadow-md bg-gradient-to-r from-green-50 to-green-100 transition-all duration-200 group\"\r\n                style={{ borderColor: \"#90EE90\" }}\r\n                onMouseEnter={(e) => {\r\n                  e.currentTarget.style.borderColor = \"#32CD32\"\r\n                }}\r\n                onMouseLeave={(e) => {\r\n                  e.currentTarget.style.borderColor = \"#90EE90\"\r\n                }}\r\n              >\r\n                <div className=\"flex items-center gap-2\">\r\n                  <Star className=\"h-5 w-5 text-green-600\" />\r\n                  <h3 className=\"text-lg font-semibold text-gray-900\">Highly Recommended</h3>\r\n                </div>\r\n              </div>\r\n              {/* Content Box */}\r\n              <div className=\"border border-gray-200 rounded-md p-4 shadow-md bg-white\">\r\n                <div className=\"space-y-4\">\r\n                  <div className=\"flex items-center\">\r\n                    <input\r\n                      type=\"checkbox\"\r\n                      id=\"maleFemaleSepa\"\r\n                      checked={maleFemaleSepa}\r\n                      onChange={(e) => setMaleFemaleSepa(e.target.checked)}\r\n                      className=\"h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded\"\r\n                    />\r\n                    <label htmlFor=\"maleFemaleSepa\" className=\"ml-2 block text-sm text-gray-900\">\r\n                      Male/Female Separation\r\n                    </label>\r\n                  </div>\r\n                  <div className=\"flex items-center\">\r\n                    <input\r\n                      type=\"checkbox\"\r\n                      id=\"airConditioning\"\r\n                      checked={airConditioning}\r\n                      onChange={(e) => setAirConditioning(e.target.checked)}\r\n                      className=\"h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded\"\r\n                    />\r\n                    <label htmlFor=\"airConditioning\" className=\"ml-2 block text-sm text-gray-900\">\r\n                      Air Conditioning\r\n                    </label>\r\n                  </div>\r\n                  <div className=\"flex items-center\">\r\n                    <input\r\n                      type=\"checkbox\"\r\n                      id=\"maleFemaleSt\"\r\n                      checked={maleFemaleSt}\r\n                      onChange={(e) => setMaleFemaleSt(e.target.checked)}\r\n                      className=\"h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded\"\r\n                    />\r\n                    <label htmlFor=\"maleFemaleSt\" className=\"ml-2 block text-sm text-gray-900\">\r\n                      Male/Female Staff\r\n                    </label>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Footer Buttons */}\r\n          <div className=\"flex flex-col sm:flex-row gap-3 mt-8 pt-6 border-t\">\r\n            <button\r\n              type=\"button\"\r\n              onClick={() => setShowBookingModal(false)}\r\n              className=\"flex-1 px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors font-medium\"\r\n            >\r\n              Cancel\r\n            </button>\r\n            <button\r\n              type=\"submit\"\r\n              className=\"flex-1 px-6 py-3 bg-gradient-to-r from-blue-400 to-purple-400 text-white rounded-lg hover:from-blue-500 hover:to-purple-500 transition-colors font-medium shadow-md\"\r\n            >\r\n              Submit Booking Request\r\n            </button>\r\n          </div>\r\n        </form>\r\n      </div>\r\n    </div>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;AAIA;AAEA;AAAA;AAAA;AAAA;AANA;;;;AA+BO,SAAS,aAAa,KAAwB;IACnD,MAAM,EACJ,mBAAmB,EACnB,YAAY,EACZ,eAAe,EACf,aAAa,EACb,gBAAgB,EAChB,aAAa,EACb,gBAAgB,EAChB,SAAS,EACT,YAAY,EACZ,UAAU,EACV,aAAa,EACb,YAAY,EACZ,eAAe,EACf,eAAe,EACf,kBAAkB,EAClB,cAAc,EACd,iBAAiB,EACjB,cAAc,EACd,iBAAiB,EACjB,eAAe,EAChB,GAAG;IAEJ,uCAAuC;IACvC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,wBAAwB,0BAA0B,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrE,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAChB,QAAQ,GAAG,CAAC;QACZ,oBAAoB;IACtB;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAAmC;;;;;;8CACjD,8OAAC;oCAAE,WAAU;8CAA6B;;;;;;;;;;;;sCAE5C,8OAAC;4BACC,SAAS,IAAM,oBAAoB;4BACnC,WAAU;sCAEV,cAAA,8OAAC;gCAAI,WAAU;gCAAU,MAAK;gCAAO,QAAO;gCAAe,SAAQ;0CACjE,cAAA,8OAAC;oCAAK,eAAc;oCAAQ,gBAAe;oCAAQ,aAAa;oCAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;8BAM3E,8OAAC;oBAAK,UAAU;oBAAc,WAAU;;sCACtC,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;;sDAEb,8OAAC;4CACC,WAAU;4CACV,OAAO;gDAAE,aAAa;4CAAU;4CAChC,cAAc,CAAC;gDACb,EAAE,aAAa,CAAC,KAAK,CAAC,WAAW,GAAG;4CACtC;4CACA,cAAc,CAAC;gDACb,EAAE,aAAa,CAAC,KAAK,CAAC,WAAW,GAAG;4CACtC;sDAEA,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,kMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;kEAChB,8OAAC;wDAAG,WAAU;kEAAsC;;;;;;;;;;;;;;;;;sDAIxD,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;;0EACC,8OAAC;gEAAM,WAAU;0EAA+C;;;;;;0EAChE,8OAAC;gEACC,MAAK;gEACL,OAAO;gEACP,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;gEAC/C,WAAU;gEACV,aAAY;gEACZ,QAAQ;;;;;;;;;;;;kEAGZ,8OAAC;;0EACC,8OAAC;gEAAM,WAAU;0EAA+C;;;;;;0EAChE,8OAAC;gEACC,MAAK;gEACL,OAAO;gEACP,UAAU,CAAC,IAAM,aAAa,EAAE,MAAM,CAAC,KAAK;gEAC5C,WAAU;gEACV,aAAY;gEACZ,QAAQ;;;;;;;;;;;;kEAGZ,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAM,WAAU;0EAA+C;;;;;;0EAChE,8OAAC;gEACC,MAAK;gEACL,OAAO;gEACP,UAAU,CAAC,IAAM,iBAAiB,EAAE,MAAM,CAAC,KAAK;gEAChD,WAAU;gEACV,aAAY;gEACZ,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAQlB,8OAAC;oCAAI,WAAU;;sDAEb,8OAAC;4CACC,WAAU;4CACV,OAAO;gDAAE,aAAa;4CAAU;4CAChC,cAAc,CAAC;gDACb,EAAE,aAAa,CAAC,KAAK,CAAC,WAAW,GAAG;4CACtC;4CACA,cAAc,CAAC;gDACb,EAAE,aAAa,CAAC,KAAK,CAAC,WAAW,GAAG;4CACtC;sDAEA,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,0MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;kEACpB,8OAAC;wDAAG,WAAU;kEAAsC;;;;;;;;;;;;;;;;;sDAIxD,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;;0EACC,8OAAC;gEAAM,WAAU;0EAA+C;;;;;;0EAChE,8OAAC;gEACC,OAAO;gEACP,UAAU,CAAC,IAAM,aAAa,EAAE,MAAM,CAAC,KAAK;gEAC5C,WAAU;gEACV,QAAQ;;kFAER,8OAAC;wEAAO,OAAM;kFAAG;;;;;;kFACjB,8OAAC;wEAAO,OAAM;kFAAU;;;;;;kFACxB,8OAAC;wEAAO,OAAM;kFAAa;;;;;;kFAC3B,8OAAC;wEAAO,OAAM;kFAAS;;;;;;kFACvB,8OAAC;wEAAO,OAAM;kFAAW;;;;;;kFACzB,8OAAC;wEAAO,OAAM;kFAAS;;;;;;kFACvB,8OAAC;wEAAO,OAAM;kFAAQ;;;;;;kFACtB,8OAAC;wEAAO,OAAM;kFAAQ;;;;;;;;;;;;;;;;;;kEAG1B,8OAAC;;0EACC,8OAAC;gEAAM,WAAU;0EAA+C;;;;;;0EAChE,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEACC,MAAK;wEACL,OAAO;wEACP,UAAU,CAAC,IAAM,kBAAkB,EAAE,MAAM,CAAC,KAAK;wEACjD,WAAU;wEACV,QAAQ;;;;;;kFAEV,8OAAC,0MAAA,CAAA,WAAQ;wEAAC,WAAU;;;;;;;;;;;;;;;;;;kEAGxB,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAM,WAAU;0EAA+C;;;;;;0EAChE,8OAAC;gEACC,MAAK;gEACL,OAAO;gEACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;gEAC7C,WAAU;gEACV,aAAY;gEACZ,KAAI;gEACJ,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAQlB,8OAAC;oCAAI,WAAU;;sDAEb,8OAAC;4CACC,WAAU;4CACV,OAAO;gDAAE,aAAa;4CAAU;4CAChC,cAAc,CAAC;gDACb,EAAE,aAAa,CAAC,KAAK,CAAC,WAAW,GAAG;4CACtC;4CACA,cAAc,CAAC;gDACb,EAAE,aAAa,CAAC,KAAK,CAAC,WAAW,GAAG;4CACtC;sDAEA,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,0MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;kEACpB,8OAAC;wDAAG,WAAU;kEAAsC;;;;;;;;;;;;;;;;;sDAIxD,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;;0EACC,8OAAC;gEAAM,WAAU;0EAA+C;;;;;;0EAChE,8OAAC;gEACC,OAAO;gEACP,UAAU,CAAC,IAAM,aAAa,EAAE,MAAM,CAAC,KAAK;gEAC5C,WAAU;gEACV,QAAQ;;kFAER,8OAAC;wEAAO,OAAM;kFAAG;;;;;;kFACjB,8OAAC;wEAAO,OAAM;kFAAmB;;;;;;kFACjC,8OAAC;wEAAO,OAAM;kFAAyB;;;;;;;;;;;;;;;;;;kEAG3C,8OAAC;;0EACC,8OAAC;gEAAM,WAAU;0EAA+C;;;;;;0EAChE,8OAAC;gEACC,OAAO;gEACP,UAAU,CAAC,IAAM,0BAA0B,EAAE,MAAM,CAAC,KAAK;gEACzD,WAAU;gEACV,QAAQ;;kFAER,8OAAC;wEAAO,OAAM;kFAAG;;;;;;kFACjB,8OAAC;wEAAO,OAAM;kFAAkB;;;;;;kFAChC,8OAAC;wEAAO,OAAM;kFAAmB;;;;;;;;;;;;;;;;;;kEAGrC,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAM,WAAU;0EAA+C;;;;;;0EAChE,8OAAC;gEACC,OAAO;gEACP,UAAU,CAAC,IAAM,iBAAiB,EAAE,MAAM,CAAC,KAAK;gEAChD,WAAU;gEACV,QAAQ;;kFAER,8OAAC;wEAAO,OAAM;kFAAG;;;;;;kFACjB,8OAAC;wEAAO,OAAM;kFAAU;;;;;;kFACxB,8OAAC;wEAAO,OAAM;kFAAM;;;;;;kFACpB,8OAAC;wEAAO,OAAM;kFAAU;;;;;;kFACxB,8OAAC;wEAAO,OAAM;kFAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAQhC,8OAAC;oCAAI,WAAU;;sDAEb,8OAAC;4CACC,WAAU;4CACV,OAAO;gDAAE,aAAa;4CAAU;4CAChC,cAAc,CAAC;gDACb,EAAE,aAAa,CAAC,KAAK,CAAC,WAAW,GAAG;4CACtC;4CACA,cAAc,CAAC;gDACb,EAAE,aAAa,CAAC,KAAK,CAAC,WAAW,GAAG;4CACtC;sDAEA,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,kMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;kEAChB,8OAAC;wDAAG,WAAU;kEAAsC;;;;;;;;;;;;;;;;;sDAIxD,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEACC,MAAK;gEACL,IAAG;gEACH,SAAS;gEACT,UAAU,CAAC,IAAM,kBAAkB,EAAE,MAAM,CAAC,OAAO;gEACnD,WAAU;;;;;;0EAEZ,8OAAC;gEAAM,SAAQ;gEAAiB,WAAU;0EAAmC;;;;;;;;;;;;kEAI/E,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEACC,MAAK;gEACL,IAAG;gEACH,SAAS;gEACT,UAAU,CAAC,IAAM,mBAAmB,EAAE,MAAM,CAAC,OAAO;gEACpD,WAAU;;;;;;0EAEZ,8OAAC;gEAAM,SAAQ;gEAAkB,WAAU;0EAAmC;;;;;;;;;;;;kEAIhF,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEACC,MAAK;gEACL,IAAG;gEACH,SAAS;gEACT,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,OAAO;gEACjD,WAAU;;;;;;0EAEZ,8OAAC;gEAAM,SAAQ;gEAAe,WAAU;0EAAmC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAUrF,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,MAAK;oCACL,SAAS,IAAM,oBAAoB;oCACnC,WAAU;8CACX;;;;;;8CAGD,8OAAC;oCACC,MAAK;oCACL,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQb", "debugId": null}}, {"offset": {"line": 1770, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/booking-management/booking-hall/src/app/booking/components/all-booking-modal.tsx"], "sourcesContent": ["\"use client\"\r\n\r\ninterface AllBookingsModalProps {\r\n  setShowAllBookingsModal: (show: boolean) => void\r\n  bookings: any[]\r\n  selectedEventType: string\r\n  setSelectedEventType: (type: string) => void\r\n  eventCategories: any[]\r\n}\r\n\r\nexport function AllBookingsModal({\r\n  setShowAllBookingsModal,\r\n  bookings,\r\n  selectedEventType,\r\n  setSelectedEventType,\r\n  eventCategories,\r\n}: AllBookingsModalProps) {\r\n  const getStatusColor = (status: string) => {\r\n    switch (status) {\r\n      case \"confirmed\":\r\n        return \"bg-green-100 text-green-800\"\r\n      case \"pending\":\r\n        return \"bg-yellow-100 text-yellow-800\"\r\n      case \"cancelled\":\r\n        return \"bg-red-100 text-red-800\"\r\n      default:\r\n        return \"bg-gray-100 text-gray-800\"\r\n    }\r\n  }\r\n\r\n  const filteredBookings =\r\n    selectedEventType === \"all\" ? bookings : bookings.filter((booking) => booking.eventType === selectedEventType)\r\n\r\n  return (\r\n    <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\">\r\n      <div className=\"bg-white rounded-lg shadow-xl max-w-6xl w-full max-h-[90vh] overflow-y-auto\">\r\n        {/* Header */}\r\n        <div className=\"flex items-center justify-between p-6 border-b\">\r\n          <div>\r\n            <h3 className=\"text-xl font-semibold text-gray-900\">All Bookings</h3>\r\n            <p className=\"text-sm text-gray-600\">Manage and view all booking records</p>\r\n          </div>\r\n          <button\r\n            onClick={() => setShowAllBookingsModal(false)}\r\n            className=\"text-gray-400 hover:text-gray-600 transition-colors\"\r\n          >\r\n            <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\r\n            </svg>\r\n          </button>\r\n        </div>\r\n\r\n        {/* Filter */}\r\n        <div className=\"p-4 border-b\">\r\n          <div className=\"flex items-center justify-between\">\r\n            <span className=\"text-sm text-gray-500\">Showing {bookings.length} total bookings</span>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Bookings List */}\r\n        <div className=\"p-6 space-y-4 max-h-96 overflow-y-auto\">\r\n          {filteredBookings.map((booking, index) => {\r\n            // Define border colors similar to calendar\r\n            const borderColors = [\r\n              { default: \"#add8e6\", hover: \"#87ceeb\" }, // light blue\r\n              { default: \"#ffb6c1\", hover: \"#ff69b4\" }, // light pink\r\n              { default: \"#ffffe0\", hover: \"#fdfd96\" }, // light yellow\r\n              { default: \"#90EE90\", hover: \"#32CD32\" }, // light green\r\n            ]\r\n\r\n            const colorIndex = index % borderColors.length\r\n            const borderColor = borderColors[colorIndex]\r\n\r\n            return (\r\n              <div\r\n                key={booking.id}\r\n                className=\"border-2 rounded-lg p-3 hover:shadow-md transition-all duration-200\"\r\n                style={{ borderColor: borderColor.default }}\r\n                onMouseEnter={(e) => {\r\n                  e.currentTarget.style.borderColor = borderColor.hover\r\n                }}\r\n                onMouseLeave={(e) => {\r\n                  e.currentTarget.style.borderColor = borderColor.default\r\n                }}\r\n              >\r\n                <div className=\"flex items-start justify-between\">\r\n                  <div className=\"flex-1\">\r\n                    <div className=\"flex items-center gap-2 mb-1\">\r\n                      <h4 className=\"font-semibold text-gray-900 text-sm\">{booking.customerName}</h4>\r\n                      <span\r\n                        className={`px-2 py-0.5 rounded-full text-xs font-medium ${getStatusColor(booking.status)}`}\r\n                      >\r\n                        {booking.status}\r\n                      </span>\r\n                    </div>\r\n                    <div className=\"grid grid-cols-1 md:grid-cols-3 gap-2 text-xs text-gray-600\">\r\n                      <div>\r\n                        <span className=\"font-medium\">Event:</span> {booking.eventType}\r\n                      </div>\r\n                      <div>\r\n                        <span className=\"font-medium\">Date:</span> {booking.date}\r\n                      </div>\r\n                      <div>\r\n                        <span className=\"font-medium\">Time:</span> {booking.time}\r\n                      </div>\r\n                      <div>\r\n                        <span className=\"font-medium\">Hall:</span> {booking.hall}\r\n                      </div>\r\n                      <div>\r\n                        <span className=\"font-medium\">Guests:</span> {booking.guests}\r\n                      </div>\r\n                      <div>\r\n                        <span className=\"font-medium\">Amount:</span> ₹{booking.amount.toLocaleString()}\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                  <div className=\"flex gap-1\">\r\n                    <button className=\"px-2 py-1 text-xs bg-blue-100 text-blue-700 rounded hover:bg-blue-200 transition-colors\">\r\n                      Edit\r\n                    </button>\r\n                    <button className=\"px-2 py-1 text-xs bg-red-100 text-red-700 rounded hover:bg-red-200 transition-colors\">\r\n                      Delete\r\n                    </button>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            )\r\n          })}\r\n        </div>\r\n\r\n        {/* Footer */}\r\n        <div className=\"p-6 border-t bg-gray-50 flex justify-end\">\r\n          <button\r\n            onClick={() => setShowAllBookingsModal(false)}\r\n            className=\"px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\"\r\n          >\r\n            Close\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;;AAUO,SAAS,iBAAiB,EAC/B,uBAAuB,EACvB,QAAQ,EACR,iBAAiB,EACjB,oBAAoB,EACpB,eAAe,EACO;IACtB,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,mBACJ,sBAAsB,QAAQ,WAAW,SAAS,MAAM,CAAC,CAAC,UAAY,QAAQ,SAAS,KAAK;IAE9F,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAAsC;;;;;;8CACpD,8OAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;sCAEvC,8OAAC;4BACC,SAAS,IAAM,wBAAwB;4BACvC,WAAU;sCAEV,cAAA,8OAAC;gCAAI,WAAU;gCAAU,MAAK;gCAAO,QAAO;gCAAe,SAAQ;0CACjE,cAAA,8OAAC;oCAAK,eAAc;oCAAQ,gBAAe;oCAAQ,aAAa;oCAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;8BAM3E,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAK,WAAU;;gCAAwB;gCAAS,SAAS,MAAM;gCAAC;;;;;;;;;;;;;;;;;8BAKrE,8OAAC;oBAAI,WAAU;8BACZ,iBAAiB,GAAG,CAAC,CAAC,SAAS;wBAC9B,2CAA2C;wBAC3C,MAAM,eAAe;4BACnB;gCAAE,SAAS;gCAAW,OAAO;4BAAU;4BACvC;gCAAE,SAAS;gCAAW,OAAO;4BAAU;4BACvC;gCAAE,SAAS;gCAAW,OAAO;4BAAU;4BACvC;gCAAE,SAAS;gCAAW,OAAO;4BAAU;yBACxC;wBAED,MAAM,aAAa,QAAQ,aAAa,MAAM;wBAC9C,MAAM,cAAc,YAAY,CAAC,WAAW;wBAE5C,qBACE,8OAAC;4BAEC,WAAU;4BACV,OAAO;gCAAE,aAAa,YAAY,OAAO;4BAAC;4BAC1C,cAAc,CAAC;gCACb,EAAE,aAAa,CAAC,KAAK,CAAC,WAAW,GAAG,YAAY,KAAK;4BACvD;4BACA,cAAc,CAAC;gCACb,EAAE,aAAa,CAAC,KAAK,CAAC,WAAW,GAAG,YAAY,OAAO;4BACzD;sCAEA,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAAuC,QAAQ,YAAY;;;;;;kEACzE,8OAAC;wDACC,WAAW,CAAC,6CAA6C,EAAE,eAAe,QAAQ,MAAM,GAAG;kEAE1F,QAAQ,MAAM;;;;;;;;;;;;0DAGnB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;;0EACC,8OAAC;gEAAK,WAAU;0EAAc;;;;;;4DAAa;4DAAE,QAAQ,SAAS;;;;;;;kEAEhE,8OAAC;;0EACC,8OAAC;gEAAK,WAAU;0EAAc;;;;;;4DAAY;4DAAE,QAAQ,IAAI;;;;;;;kEAE1D,8OAAC;;0EACC,8OAAC;gEAAK,WAAU;0EAAc;;;;;;4DAAY;4DAAE,QAAQ,IAAI;;;;;;;kEAE1D,8OAAC;;0EACC,8OAAC;gEAAK,WAAU;0EAAc;;;;;;4DAAY;4DAAE,QAAQ,IAAI;;;;;;;kEAE1D,8OAAC;;0EACC,8OAAC;gEAAK,WAAU;0EAAc;;;;;;4DAAc;4DAAE,QAAQ,MAAM;;;;;;;kEAE9D,8OAAC;;0EACC,8OAAC;gEAAK,WAAU;0EAAc;;;;;;4DAAc;4DAAG,QAAQ,MAAM,CAAC,cAAc;;;;;;;;;;;;;;;;;;;kDAIlF,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAO,WAAU;0DAA0F;;;;;;0DAG5G,8OAAC;gDAAO,WAAU;0DAAuF;;;;;;;;;;;;;;;;;;2BA7CxG,QAAQ,EAAE;;;;;oBAoDrB;;;;;;8BAIF,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBACC,SAAS,IAAM,wBAAwB;wBACvC,WAAU;kCACX;;;;;;;;;;;;;;;;;;;;;;AAOX", "debugId": null}}, {"offset": {"line": 2147, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/booking-management/booking-hall/src/app/booking/components/booking-data.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nexport function BookingData() {\r\n  return (\r\n    <div>\r\n      {/* This component will show booking data when modal opens */}\r\n    </div>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;;AAEO,SAAS;IACd,qBACE,8OAAC;;;;;AAIL", "debugId": null}}, {"offset": {"line": 2166, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/booking-management/booking-hall/src/app/booking/components/data/booking-data.tsx"], "sourcesContent": ["// Event categories\r\nexport const eventCategories = [\r\n  { value: \"all\", label: \"All Events\" },\r\n  { value: \"Wedding\", label: \"Wedding\" },\r\n  { value: \"Birthday\", label: \"Birthday\" },\r\n  { value: \"Milad\", label: \"Milad\" },\r\n  { value: \"<PERSON><PERSON><PERSON>\", label: \"<PERSON><PERSON><PERSON>\" },\r\n  { value: \"Valima\", label: \"Valima\" },\r\n  { value: \"Engagement\", label: \"Engagement\" },\r\n  { value: \"Anniversary\", label: \"Anniversary\" },\r\n  { value: \"Other\", label: \"Other\" },\r\n]\r\n\r\n// Sample booking data with more dates\r\nexport const bookings = [\r\n  {\r\n    id: 1,\r\n    customerName: \"Ahmed Ali\",\r\n    eventType: \"Wedding\",\r\n    date: \"2025-01-10\",\r\n    time: \"6:00 PM - 11:00 PM\",\r\n    hall: \"Grand Hall A\",\r\n    guests: 300,\r\n    status: \"confirmed\",\r\n    amount: 150000,\r\n  },\r\n  {\r\n    id: 2,\r\n    customerName: \"Fatima Khan\",\r\n    eventType: \"Engagement\",\r\n    date: \"2025-01-15\",\r\n    time: \"4:00 PM - 8:00 PM\",\r\n    hall: \"Royal Hall B\",\r\n    guests: 150,\r\n    status: \"pending\",\r\n    amount: 80000,\r\n  },\r\n  {\r\n    id: 3,\r\n    customerName: \"<PERSON> Sheikh\",\r\n    eventType: \"Reception\",\r\n    date: \"2025-02-05\",\r\n    time: \"7:00 PM - 12:00 AM\",\r\n    hall: \"Premium Hall C\",\r\n    guests: 250,\r\n    status: \"confirmed\",\r\n    amount: 120000,\r\n  },\r\n  {\r\n    id: 4,\r\n    customerName: \"Zara Ahmed\",\r\n    eventType: \"Wedding\",\r\n    date: \"2025-02-18\",\r\n    time: \"5:00 PM - 10:00 PM\",\r\n    hall: \"Grand Hall A\",\r\n    guests: 400,\r\n    status: \"confirmed\",\r\n    amount: 200000,\r\n  },\r\n  {\r\n    id: 5,\r\n    customerName: \"Omar Khan\",\r\n    eventType: \"Birthday Party\",\r\n    date: \"2025-03-12\",\r\n    time: \"3:00 PM - 7:00 PM\",\r\n    hall: \"Royal Hall B\",\r\n    guests: 100,\r\n    status: \"pending\",\r\n    amount: 50000,\r\n  },\r\n  {\r\n    id: 6,\r\n    customerName: \"Test User\",\r\n    eventType: \"Wedding\",\r\n    date: \"2025-03-25\",\r\n    time: \"6:00 PM - 11:00 PM\",\r\n    hall: \"Grand Hall A\",\r\n    guests: 200,\r\n    status: \"confirmed\",\r\n    amount: 100000,\r\n  },\r\n  // July 2025 (current month example)\r\n  {\r\n    id: 7,\r\n    customerName: \"Ali Raza\",\r\n    eventType: \"Valima\",\r\n    date: \"2025-07-10\",\r\n    time: \"6:00 PM - 11:00 PM\",\r\n    hall: \"Grand Hall A\",\r\n    guests: 200,\r\n    status: \"confirmed\",\r\n    amount: 100000,\r\n  },\r\n  {\r\n    id: 8,\r\n    customerName: \"Sana Mir\",\r\n    eventType: \"Aqiqa\",\r\n    date: \"2025-07-15\",\r\n    time: \"2:00 PM - 6:00 PM\",\r\n    hall: \"Royal Hall B\",\r\n    guests: 120,\r\n    status: \"pending\",\r\n    amount: 60000,\r\n  },\r\n  {\r\n    id: 9,\r\n    customerName: \"Bilal Aslam\",\r\n    eventType: \"Engagement\",\r\n    date: \"2025-07-22\",\r\n    time: \"4:00 PM - 8:00 PM\",\r\n    hall: \"Premium Hall C\",\r\n    guests: 180,\r\n    status: \"confirmed\",\r\n    amount: 90000,\r\n  },\r\n  {\r\n    id: 10,\r\n    customerName: \"Ahmed Ali\",\r\n    eventType: \"Wedding\",\r\n    date: \"2024-01-15\",\r\n    time: \"6:00 PM - 11:00 PM\",\r\n    hall: \"Grand Hall A\",\r\n    guests: 300,\r\n    status: \"confirmed\",\r\n    amount: 150000,\r\n  },\r\n  {\r\n    id: 11,\r\n    customerName: \"Fatima Khan\",\r\n    eventType: \"Engagement\",\r\n    date: \"2024-01-16\",\r\n    time: \"4:00 PM - 8:00 PM\",\r\n    hall: \"Royal Hall B\",\r\n    guests: 150,\r\n    status: \"pending\",\r\n    amount: 80000,\r\n  },\r\n  {\r\n    id: 12,\r\n    customerName: \"Hassan Sheikh\",\r\n    eventType: \"Reception\",\r\n    date: \"2024-01-17\",\r\n    time: \"7:00 PM - 12:00 AM\",\r\n    hall: \"Premium Hall C\",\r\n    guests: 250,\r\n    status: \"confirmed\",\r\n    amount: 120000,\r\n  },\r\n  {\r\n    id: 13,\r\n    customerName: \"Zara Ahmed\",\r\n    eventType: \"Wedding\",\r\n    date: \"2024-01-20\",\r\n    time: \"5:00 PM - 10:00 PM\",\r\n    hall: \"Grand Hall A\",\r\n    guests: 400,\r\n    status: \"confirmed\",\r\n    amount: 200000,\r\n  },\r\n  {\r\n    id: 14,\r\n    customerName: \"Omar Khan\",\r\n    eventType: \"Birthday Party\",\r\n    date: \"2024-01-22\",\r\n    time: \"3:00 PM - 7:00 PM\",\r\n    hall: \"Royal Hall B\",\r\n    guests: 100,\r\n    status: \"pending\",\r\n    amount: 50000,\r\n  },\r\n  {\r\n    id: 15,\r\n    customerName: \"Future User\",\r\n    eventType: \"Wedding\",\r\n    date: \"2025-08-10\",\r\n    time: \"6:00 PM - 11:00 PM\",\r\n    hall: \"Grand Hall A\",\r\n    guests: 250,\r\n    status: \"confirmed\",\r\n    amount: 120000,\r\n  },\r\n  {\r\n    id: 16,\r\n    customerName: \"Next Month User\",\r\n    eventType: \"Birthday\",\r\n    date: \"2025-09-15\",\r\n    time: \"3:00 PM - 7:00 PM\",\r\n    hall: \"Royal Hall B\",\r\n    guests: 80,\r\n    status: \"pending\",\r\n    amount: 40000,\r\n  },\r\n]\r\n"], "names": [], "mappings": "AAAA,mBAAmB;;;;;AACZ,MAAM,kBAAkB;IAC7B;QAAE,OAAO;QAAO,OAAO;IAAa;IACpC;QAAE,OAAO;QAAW,OAAO;IAAU;IACrC;QAAE,OAAO;QAAY,OAAO;IAAW;IACvC;QAAE,OAAO;QAAS,OAAO;IAAQ;IACjC;QAAE,OAAO;QAAS,OAAO;IAAQ;IACjC;QAAE,OAAO;QAAU,OAAO;IAAS;IACnC;QAAE,OAAO;QAAc,OAAO;IAAa;IAC3C;QAAE,OAAO;QAAe,OAAO;IAAc;IAC7C;QAAE,OAAO;QAAS,OAAO;IAAQ;CAClC;AAGM,MAAM,WAAW;IACtB;QACE,IAAI;QACJ,cAAc;QACd,WAAW;QACX,MAAM;QACN,MAAM;QACN,MAAM;QACN,QAAQ;QACR,QAAQ;QACR,QAAQ;IACV;IACA;QACE,IAAI;QACJ,cAAc;QACd,WAAW;QACX,MAAM;QACN,MAAM;QACN,MAAM;QACN,QAAQ;QACR,QAAQ;QACR,QAAQ;IACV;IACA;QACE,IAAI;QACJ,cAAc;QACd,WAAW;QACX,MAAM;QACN,MAAM;QACN,MAAM;QACN,QAAQ;QACR,QAAQ;QACR,QAAQ;IACV;IACA;QACE,IAAI;QACJ,cAAc;QACd,WAAW;QACX,MAAM;QACN,MAAM;QACN,MAAM;QACN,QAAQ;QACR,QAAQ;QACR,QAAQ;IACV;IACA;QACE,IAAI;QACJ,cAAc;QACd,WAAW;QACX,MAAM;QACN,MAAM;QACN,MAAM;QACN,QAAQ;QACR,QAAQ;QACR,QAAQ;IACV;IACA;QACE,IAAI;QACJ,cAAc;QACd,WAAW;QACX,MAAM;QACN,MAAM;QACN,MAAM;QACN,QAAQ;QACR,QAAQ;QACR,QAAQ;IACV;IACA,oCAAoC;IACpC;QACE,IAAI;QACJ,cAAc;QACd,WAAW;QACX,MAAM;QACN,MAAM;QACN,MAAM;QACN,QAAQ;QACR,QAAQ;QACR,QAAQ;IACV;IACA;QACE,IAAI;QACJ,cAAc;QACd,WAAW;QACX,MAAM;QACN,MAAM;QACN,MAAM;QACN,QAAQ;QACR,QAAQ;QACR,QAAQ;IACV;IACA;QACE,IAAI;QACJ,cAAc;QACd,WAAW;QACX,MAAM;QACN,MAAM;QACN,MAAM;QACN,QAAQ;QACR,QAAQ;QACR,QAAQ;IACV;IACA;QACE,IAAI;QACJ,cAAc;QACd,WAAW;QACX,MAAM;QACN,MAAM;QACN,MAAM;QACN,QAAQ;QACR,QAAQ;QACR,QAAQ;IACV;IACA;QACE,IAAI;QACJ,cAAc;QACd,WAAW;QACX,MAAM;QACN,MAAM;QACN,MAAM;QACN,QAAQ;QACR,QAAQ;QACR,QAAQ;IACV;IACA;QACE,IAAI;QACJ,cAAc;QACd,WAAW;QACX,MAAM;QACN,MAAM;QACN,MAAM;QACN,QAAQ;QACR,QAAQ;QACR,QAAQ;IACV;IACA;QACE,IAAI;QACJ,cAAc;QACd,WAAW;QACX,MAAM;QACN,MAAM;QACN,MAAM;QACN,QAAQ;QACR,QAAQ;QACR,QAAQ;IACV;IACA;QACE,IAAI;QACJ,cAAc;QACd,WAAW;QACX,MAAM;QACN,MAAM;QACN,MAAM;QACN,QAAQ;QACR,QAAQ;QACR,QAAQ;IACV;IACA;QACE,IAAI;QACJ,cAAc;QACd,WAAW;QACX,MAAM;QACN,MAAM;QACN,MAAM;QACN,QAAQ;QACR,QAAQ;QACR,QAAQ;IACV;IACA;QACE,IAAI;QACJ,cAAc;QACd,WAAW;QACX,MAAM;QACN,MAAM;QACN,MAAM;QACN,QAAQ;QACR,QAAQ;QACR,QAAQ;IACV;CACD", "debugId": null}}, {"offset": {"line": 2394, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/booking-management/booking-hall/src/app/booking/page.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState } from \"react\"\nimport { CalendarDays } from \"lucide-react\"\n\n// Components\nimport { BookingStats } from \"../components/booking-stats\"\nimport { BookingActionBar } from \"./components/booking-action-bar\"\nimport { BookingCalendar } from \"./components/booking calender\"\nimport { BookingModal } from \"./components/booking-modal\"\nimport { AllBookingsModal } from \"./components/all-booking-modal\"\nimport { BookingData } from \"./components/booking-data\"\n\n// Data\nimport { bookings, eventCategories } from \"./components/data/booking-data\"\n\nexport default function BookingPage() {\n  const [selectedDate, setSelectedDate] = useState(new Date().toISOString().split(\"T\")[0])\n  const [showBookingModal, setShowBookingModal] = useState(false)\n  const [newBookingDate, setNewBookingDate] = useState(\"\")\n  const [newBookingTime, setNewBookingTime] = useState(\"\")\n\n  // Registration form states\n  const [customerName, setCustomerName] = useState(\"\")\n  const [customerPhone, setCustomerPhone] = useState(\"\")\n  const [customerEmail, setCustomerEmail] = useState(\"\")\n  const [eventType, setEventType] = useState(\"\")\n  const [guestCount, setGuestCount] = useState(\"\")\n  const [selectedHall, setSelectedHall] = useState(\"\")\n  const [specialRequests, setSpecialRequests] = useState(\"\")\n\n  const [showAllBookingsModal, setShowAllBookingsModal] = useState(false)\n  const [selectedEventType, setSelectedEventType] = useState(\"all\")\n  const [showCalendar, setShowCalendar] = useState(false)\n  const [currentCalendarMonth, setCurrentCalendarMonth] = useState(0)\n  const [selectedCalendarDate, setSelectedCalendarDate] = useState<string | null>(null)\n\n  return (\n    <>\n      <div className=\"flex-1 overflow-hidden bg-gray-50\">\n        {/* Header */}\n        <header className=\"flex h-16 items-end justify-between border-2 ml-3 mr-2 rounded-[12px] bg-white px-4 pb-4\">\n          <div className=\"flex items-center gap-3\">\n            <h1 className=\"text-2xl text-black font-semibold\">Booking Management</h1>\n          </div>\n          <button className=\"rounded-full bg-gray-100 p-2 hover:bg-gray-200 transition-colors\">\n            <CalendarDays className=\"h-7 w-6 text-green-400\" />\n          </button>\n        </header>\n\n        {/* Main Content */}\n        <main className=\"p-4 h-full overflow-auto\">\n          {/* Stats Cards */}\n          <BookingStats />\n\n          {/* Action Bar */}\n          <BookingActionBar\n            showCalendar={showCalendar}\n            setShowCalendar={setShowCalendar}\n            setShowBookingModal={setShowBookingModal}\n            setShowAllBookingsModal={setShowAllBookingsModal}\n          />\n\n          {/* Calendar Section */}\n          {showCalendar && (\n            <BookingCalendar\n              currentCalendarMonth={currentCalendarMonth}\n              setCurrentCalendarMonth={setCurrentCalendarMonth}\n              selectedDate={selectedDate}\n              setSelectedCalendarDate={setSelectedCalendarDate}\n              bookings={bookings}\n            />\n          )}\n\n          {/* Booking Data */}\n          <BookingData />\n        </main>\n      </div>\n\n      {/* Modals */}\n      {showBookingModal && (\n        <BookingModal\n          setShowBookingModal={setShowBookingModal}\n          customerName={customerName}\n          setCustomerName={setCustomerName}\n          customerPhone={customerPhone}\n          setCustomerPhone={setCustomerPhone}\n          customerEmail={customerEmail}\n          setCustomerEmail={setCustomerEmail}\n          eventType={eventType}\n          setEventType={setEventType}\n          guestCount={guestCount}\n          setGuestCount={setGuestCount}\n          selectedHall={selectedHall}\n          setSelectedHall={setSelectedHall}\n          specialRequests={specialRequests}\n          setSpecialRequests={setSpecialRequests}\n          newBookingDate={newBookingDate}\n          setNewBookingDate={setNewBookingDate}\n          newBookingTime={newBookingTime}\n          setNewBookingTime={setNewBookingTime}\n          eventCategories={eventCategories}\n        />\n      )}\n\n      {showAllBookingsModal && (\n        <AllBookingsModal\n          setShowAllBookingsModal={setShowAllBookingsModal}\n          bookings={bookings}\n          selectedEventType={selectedEventType}\n          setSelectedEventType={setSelectedEventType}\n          eventCategories={eventCategories}\n        />\n      )}\n    </>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AAEA,OAAO;AACP;AAdA;;;;;;;;;;;AAgBe,SAAS;IACtB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;IACvF,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAErD,2BAA2B;IAC3B,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvD,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjE,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjE,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAEhF,qBACE;;0BACE,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAO,WAAU;;0CAChB,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAG,WAAU;8CAAoC;;;;;;;;;;;0CAEpD,8OAAC;gCAAO,WAAU;0CAChB,cAAA,8OAAC,sNAAA,CAAA,eAAY;oCAAC,WAAU;;;;;;;;;;;;;;;;;kCAK5B,8OAAC;wBAAK,WAAU;;0CAEd,8OAAC,6IAAA,CAAA,eAAY;;;;;0CAGb,8OAAC,gKAAA,CAAA,mBAAgB;gCACf,cAAc;gCACd,iBAAiB;gCACjB,qBAAqB;gCACrB,yBAAyB;;;;;;4BAI1B,8BACC,8OAAC,yJAAA,CAAA,kBAAe;gCACd,sBAAsB;gCACtB,yBAAyB;gCACzB,cAAc;gCACd,yBAAyB;gCACzB,UAAU,+JAAA,CAAA,WAAQ;;;;;;0CAKtB,8OAAC,uJAAA,CAAA,cAAW;;;;;;;;;;;;;;;;;YAKf,kCACC,8OAAC,wJAAA,CAAA,eAAY;gBACX,qBAAqB;gBACrB,cAAc;gBACd,iBAAiB;gBACjB,eAAe;gBACf,kBAAkB;gBAClB,eAAe;gBACf,kBAAkB;gBAClB,WAAW;gBACX,cAAc;gBACd,YAAY;gBACZ,eAAe;gBACf,cAAc;gBACd,iBAAiB;gBACjB,iBAAiB;gBACjB,oBAAoB;gBACpB,gBAAgB;gBAChB,mBAAmB;gBACnB,gBAAgB;gBAChB,mBAAmB;gBACnB,iBAAiB,+JAAA,CAAA,kBAAe;;;;;;YAInC,sCACC,8OAAC,+JAAA,CAAA,mBAAgB;gBACf,yBAAyB;gBACzB,UAAU,+JAAA,CAAA,WAAQ;gBAClB,mBAAmB;gBACnB,sBAAsB;gBACtB,iBAAiB,+JAAA,CAAA,kBAAe;;;;;;;;AAK1C", "debugId": null}}]}