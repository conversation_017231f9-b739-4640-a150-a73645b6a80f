"use client"

import type React from "react"

import { useState } from "react"

import { User, <PERSON>, Setting<PERSON>, Star } from "lucide-react"

interface BookingModalProps {
  setShowBookingModal: (show: boolean) => void
  customerName: string
  setCustomerName: (name: string) => void
  customerPhone: string
  setCustomerPhone: (phone: string) => void
  customerEmail: string
  setCustomerEmail: (email: string) => void
  eventType: string
  setEventType: (type: string) => void
  guestCount: string
  setGuestCount: (count: string) => void
  selectedHall: string
  setSelectedHall: (hall: string) => void
  specialRequests: string
  setSpecialRequests: (requests: string) => void
  newBookingDate: string
  setNewBookingDate: (date: string) => void
  newBookingTime: string
  setNewBookingTime: (time: string) => void
  eventCategories: any[]
}

export function BookingModal(props: BookingModalProps) {
  const {
    setShowBookingModal,
    customerName,
    setCustomerName,
    customerPhone,
    setCustomerP<PERSON>,
    customerEmail,
    setCustomerEmail,
    eventType,
    setEventType,
    guestCount,
    setGuestCount,
    selectedHall,
    setSelectedHall,
    specialRequests,
    setSpecialRequests,
    newBookingDate,
    setNewBookingDate,
    newBookingTime,
    setNewBookingTime,
    eventCategories,
  } = props

  // Additional state for new form fields
  const [nicNumber, setNicNumber] = useState("")
  const [entryType, setEntryType] = useState("")
  const [decorationRequirements, setDecorationRequirements] = useState("")
  const [receptionType, setReceptionType] = useState("")
  const [maleFemaleSepa, setMaleFemaleSepa] = useState("")
  const [airConditioning, setAirConditioning] = useState(false)
  const [maleFemaleSt, setMaleFemaleSt] = useState("")

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    console.log("Form submitted with all data")
    setShowBookingModal(false)
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b bg-gradient-to-r from-blue-50 to-purple-50">
          <div>
            <h2 className="text-2xl font-bold text-gray-900">Marriage Hall Booking Form</h2>
            <p className="text-sm text-gray-600 mt-1">Please fill out all sections to complete your booking</p>
          </div>
          <button
            onClick={() => setShowBookingModal(false)}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* Form Content */}
        <form onSubmit={handleSubmit} className="p-6">
          <div className="space-y-6">
            {/* Personal Information Section */}
            <div className="space-y-3">
              {/* Heading Box */}
              <div
                className="border-2 rounded-md p-3 shadow-md bg-gradient-to-r from-blue-50 to-blue-100 transition-all duration-200 group"
                style={{ borderColor: "#add8e6" }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.borderColor = "#87ceeb"
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.borderColor = "#add8e6"
                }}
              >
                <div className="flex items-center gap-2">
                  <User className="h-5 w-5 text-blue-600" />
                  <h3 className="text-lg font-semibold text-gray-900">Personal Information</h3>
                </div>
              </div>
              {/* Content Box */}
              <div className="border border-gray-200 rounded-md p-4 shadow-md bg-white">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Full Name *</label>
                    <input
                      type="text"
                      value={customerName}
                      onChange={(e) => setCustomerName(e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      placeholder="Enter your full name"
                      required
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">NIC Number *</label>
                    <input
                      type="text"
                      value={nicNumber}
                      onChange={(e) => setNicNumber(e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      placeholder="Enter your NIC number"
                      required
                    />
                  </div>
                  <div className="md:col-span-2">
                    <label className="block text-sm font-medium text-gray-700 mb-2">Contact Number *</label>
                    <input
                      type="tel"
                      value={customerPhone}
                      onChange={(e) => setCustomerPhone(e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      placeholder="Enter your contact number"
                      required
                    />
                  </div>
                </div>
              </div>
            </div>

            {/* About Event Section */}
            <div className="space-y-3">
              {/* Heading Box */}
              <div
                className="border-2 rounded-md p-3 shadow-md bg-gradient-to-r from-pink-50 to-pink-100 transition-all duration-200 group"
                style={{ borderColor: "#ffb6c1" }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.borderColor = "#ff69b4"
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.borderColor = "#ffb6c1"
                }}
              >
                <div className="flex items-center gap-2">
                  <Calendar className="h-5 w-5 text-pink-600" />
                  <h3 className="text-lg font-semibold text-gray-900">About Event</h3>
                </div>
              </div>
              {/* Content Box */}
              <div className="border border-gray-200 rounded-md p-4 shadow-md bg-white">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Event Type *</label>
                    <select
                      value={eventType}
                      onChange={(e) => setEventType(e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-pink-500"
                      required
                    >
                      <option value="">Select Event Type</option>
                      <option value="Wedding">Wedding</option>
                      <option value="Engagement">Engagement</option>
                      <option value="Mehndi">Mehndi</option>
                      <option value="Birthday">Birthday</option>
                      <option value="Valima">Valima</option>
                      <option value="Melad">Melad</option>
                      <option value="Other">Other</option>
                    </select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Event Date *</label>
                    <div className="relative">
                      <input
                        type="date"
                        value={newBookingDate}
                        onChange={(e) => setNewBookingDate(e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-pink-500"
                        required
                      />
                      <Calendar className="absolute right-3 top-2.5 h-4 w-4 text-gray-400 pointer-events-none" />
                    </div>
                  </div>
                  <div className="md:col-span-2">
                    <label className="block text-sm font-medium text-gray-700 mb-2">Number of Guests *</label>
                    <input
                      type="number"
                      value={guestCount}
                      onChange={(e) => setGuestCount(e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-pink-500"
                      placeholder="Enter expected number of guests"
                      min="1"
                      required
                    />
                  </div>
                </div>
              </div>
            </div>

            {/* Requirements Section */}
            <div className="space-y-3">
              {/* Heading Box */}
              <div
                className="border-2 rounded-md p-3 shadow-md bg-gradient-to-r from-yellow-50 to-yellow-100 transition-all duration-200 group"
                style={{ borderColor: "#ffffe0" }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.borderColor = "#fdfd96"
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.borderColor = "#ffffe0"
                }}
              >
                <div className="flex items-center gap-2">
                  <Settings className="h-5 w-5 text-yellow-600" />
                  <h3 className="text-lg font-semibold text-gray-900">Requirements</h3>
                </div>
              </div>
              {/* Content Box */}
              <div className="border border-gray-200 rounded-md p-4 shadow-md bg-white">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Entry Type *</label>
                    <select
                      value={entryType}
                      onChange={(e) => setEntryType(e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500"
                      required
                    >
                      <option value="">Select Entry Type</option>
                      <option value="Entry with Baghi">Entry with Baghi</option>
                      <option value="Entry with Flower Doli">Entry with Flower Doli</option>
                    </select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Decoration Requirements *</label>
                    <select
                      value={decorationRequirements}
                      onChange={(e) => setDecorationRequirements(e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500"
                      required
                    >
                      <option value="">Select Decoration Type</option>
                      <option value="Original Flower">Original Flower</option>
                      <option value="Imitation Flower">Imitation Flower</option>
                    </select>
                  </div>
                  <div className="md:col-span-2">
                    <label className="block text-sm font-medium text-gray-700 mb-2">Type of Reception *</label>
                    <select
                      value={receptionType}
                      onChange={(e) => setReceptionType(e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500"
                      required
                    >
                      <option value="">Select Reception Type</option>
                      <option value="Morning">Morning</option>
                      <option value="Day">Day</option>
                      <option value="Evening">Evening</option>
                      <option value="Night">Night</option>
                    </select>
                  </div>
                </div>
              </div>
            </div>

            {/* Highly Recommended Section */}
            <div className="space-y-3">
              {/* Heading Box */}
              <div
                className="border-2 rounded-md p-3 shadow-md bg-gradient-to-r from-green-50 to-green-100 transition-all duration-200 group"
                style={{ borderColor: "#90EE90" }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.borderColor = "#32CD32"
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.borderColor = "#90EE90"
                }}
              >
                <div className="flex items-center gap-2">
                  <Star className="h-5 w-5 text-green-600" />
                  <h3 className="text-lg font-semibold text-gray-900">Additional Commitment</h3>
                </div>
              </div>
              {/* Content Box */}
              <div className="border border-gray-200 rounded-md p-4 shadow-md bg-white">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Male/Female Separation</label>
                    <select
                      value={maleFemaleSepa}
                      onChange={(e) => setMaleFemaleSepa(e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500"
                    >
                      <option value="">Select separation option</option>
                      <option value="yes">Yes</option>
                      <option value="no">No</option>
                    </select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Male/Female Staff</label>
                    <select
                      value={maleFemaleSt}
                      onChange={(e) => setMaleFemaleSt(e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500"
                    >
                      <option value="">Select staff preference</option>
                      <option value="male">Male Staff</option>
                      <option value="female">Female Staff</option>
                      <option value="both">Both</option>
                    </select>
                  </div>
                  <div className="md:col-span-2">
                    <div className="flex items-center">
                      <input
                        type="checkbox"
                        id="airConditioning"
                        checked={airConditioning}
                        onChange={(e) => setAirConditioning(e.target.checked)}
                        className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded"
                      />
                      <label htmlFor="airConditioning" className="ml-2 block text-sm text-gray-900">
                        Air Conditioning
                      </label>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Footer Buttons */}
          <div className="flex flex-col sm:flex-row gap-3 mt-8 pt-6 border-t">
            <button
              type="button"
              onClick={() => setShowBookingModal(false)}
              className="flex-1 px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors font-medium"
            >
              Cancel
            </button>
            <button
              type="submit"
              className="flex-1 px-6 py-3 bg-gradient-to-r from-blue-400 to-purple-400 text-white rounded-lg hover:from-blue-500 hover:to-purple-500 transition-colors font-medium shadow-md"
            >
              Submit Booking Request
            </button>
          </div>
        </form>
      </div>
    </div>
  )
}
