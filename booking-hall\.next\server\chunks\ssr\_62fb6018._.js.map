{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/booking-management/booking-hall/src/app/components/booking-stats.tsx"], "sourcesContent": ["\"use client\"\n\nimport { Calendar, Star, DollarSign } from \"lucide-react\"\n\nexport function BookingStats() {\n  // Define border colors similar to calendar\n  const borderColors = [\n    { default: \"#add8e6\", hover: \"#87ceeb\" }, // light blue\n    { default: \"#ffb6c1\", hover: \"#ff69b4\" }, // light pink\n    { default: \"#90EE90\", hover: \"#32CD32\" }, // light green\n  ]\n\n  return (\n    <div className=\"grid grid-cols-1 gap-4 md:grid-cols-3 mb-6\">\n      {/* Upcoming Booking */}\n      <div\n        className=\"rounded-lg border-2 bg-white p-6 shadow-sm hover:shadow-md transition-all duration-200\"\n        style={{ borderColor: borderColors[0].default }}\n        onMouseEnter={(e) => {\n          e.currentTarget.style.borderColor = borderColors[0].hover\n        }}\n        onMouseLeave={(e) => {\n          e.currentTarget.style.borderColor = borderColors[0].default\n        }}\n      >\n        <div className=\"flex items-center justify-between\">\n          <h2 className=\"font-medium text-gray-900\">Upcomming Booking</h2>\n          <Calendar className=\"h-5 w-5 text-blue-400\" />\n        </div>\n        <p className=\"mt-2 text-2xl font-bold text-gray-900\">7</p>\n        <p className=\"text-sm text-gray-500\">+1 from yesterday</p>\n      </div>\n\n      {/* Feedback Highlights */}\n      <div\n        className=\"rounded-lg border-2 bg-white p-6 shadow-sm hover:shadow-md transition-all duration-200\"\n        style={{ borderColor: borderColors[1].default }}\n        onMouseEnter={(e) => {\n          e.currentTarget.style.borderColor = borderColors[1].hover\n        }}\n        onMouseLeave={(e) => {\n          e.currentTarget.style.borderColor = borderColors[1].default\n        }}\n      >\n        <div className=\"flex items-center justify-between\">\n          <h2 className=\"font-medium text-gray-900\">Feedback Highlights</h2>\n          <Star className=\"h-5 w-5 text-pink-400\" />\n        </div>\n        <p className=\"mt-2 text-2xl font-bold text-gray-900\">95%</p>\n        <p className=\"text-sm text-gray-500\">customers satisfied</p>\n        <p className=\"text-sm text-gray-500\">📣 10 new reviews this month</p>\n      </div>\n\n      {/* Financial Summary */}\n      <div\n        className=\"rounded-lg border-2 bg-white p-6 shadow-sm hover:shadow-md transition-all duration-200\"\n        style={{ borderColor: borderColors[2].default }}\n        onMouseEnter={(e) => {\n          e.currentTarget.style.borderColor = borderColors[2].hover\n        }}\n        onMouseLeave={(e) => {\n          e.currentTarget.style.borderColor = borderColors[2].default\n        }}\n      >\n        <div className=\"flex items-center justify-between\">\n          <h2 className=\"font-medium text-gray-900\">💰 Financial Summary</h2>\n          <DollarSign className=\"h-5 w-5 text-green-400\" />\n        </div>\n        <p className=\"mt-2 text-2xl font-bold text-gray-900\">₹5,48,000</p>\n        <p className=\"text-sm text-gray-500\">+12% Growth in July</p>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAAA;AAFA;;;AAIO,SAAS;IACd,2CAA2C;IAC3C,MAAM,eAAe;QACnB;YAAE,SAAS;YAAW,OAAO;QAAU;QACvC;YAAE,SAAS;YAAW,OAAO;QAAU;QACvC;YAAE,SAAS;YAAW,OAAO;QAAU;KACxC;IAED,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBACC,WAAU;gBACV,OAAO;oBAAE,aAAa,YAAY,CAAC,EAAE,CAAC,OAAO;gBAAC;gBAC9C,cAAc,CAAC;oBACb,EAAE,aAAa,CAAC,KAAK,CAAC,WAAW,GAAG,YAAY,CAAC,EAAE,CAAC,KAAK;gBAC3D;gBACA,cAAc,CAAC;oBACb,EAAE,aAAa,CAAC,KAAK,CAAC,WAAW,GAAG,YAAY,CAAC,EAAE,CAAC,OAAO;gBAC7D;;kCAEA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAA4B;;;;;;0CAC1C,8OAAC,0MAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;;;;;;;kCAEtB,8OAAC;wBAAE,WAAU;kCAAwC;;;;;;kCACrD,8OAAC;wBAAE,WAAU;kCAAwB;;;;;;;;;;;;0BAIvC,8OAAC;gBACC,WAAU;gBACV,OAAO;oBAAE,aAAa,YAAY,CAAC,EAAE,CAAC,OAAO;gBAAC;gBAC9C,cAAc,CAAC;oBACb,EAAE,aAAa,CAAC,KAAK,CAAC,WAAW,GAAG,YAAY,CAAC,EAAE,CAAC,KAAK;gBAC3D;gBACA,cAAc,CAAC;oBACb,EAAE,aAAa,CAAC,KAAK,CAAC,WAAW,GAAG,YAAY,CAAC,EAAE,CAAC,OAAO;gBAC7D;;kCAEA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAA4B;;;;;;0CAC1C,8OAAC,kMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;;;;;;;kCAElB,8OAAC;wBAAE,WAAU;kCAAwC;;;;;;kCACrD,8OAAC;wBAAE,WAAU;kCAAwB;;;;;;kCACrC,8OAAC;wBAAE,WAAU;kCAAwB;;;;;;;;;;;;0BAIvC,8OAAC;gBACC,WAAU;gBACV,OAAO;oBAAE,aAAa,YAAY,CAAC,EAAE,CAAC,OAAO;gBAAC;gBAC9C,cAAc,CAAC;oBACb,EAAE,aAAa,CAAC,KAAK,CAAC,WAAW,GAAG,YAAY,CAAC,EAAE,CAAC,KAAK;gBAC3D;gBACA,cAAc,CAAC;oBACb,EAAE,aAAa,CAAC,KAAK,CAAC,WAAW,GAAG,YAAY,CAAC,EAAE,CAAC,OAAO;gBAC7D;;kCAEA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAA4B;;;;;;0CAC1C,8OAAC,kNAAA,CAAA,aAAU;gCAAC,WAAU;;;;;;;;;;;;kCAExB,8OAAC;wBAAE,WAAU;kCAAwC;;;;;;kCACrD,8OAAC;wBAAE,WAAU;kCAAwB;;;;;;;;;;;;;;;;;;AAI7C", "debugId": null}}, {"offset": {"line": 231, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/booking-management/booking-hall/src/app/page.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState } from \"react\"\nimport { CalendarDays } from \"lucide-react\"\n\n// Components\nimport { BookingStats } from \"./components/booking-stats\"\n\nexport default function DashboardPage() {\n\n  return (\n    <>\n      <div className=\"flex-1 overflow-hidden bg-gray-50\">\n        {/* Header */}\n        <header className=\"flex h-16 items-end justify-between border-2 ml-3 mr-2 rounded-[12px] bg-white px-4 pb-4\">\n          <div className=\"flex items-center gap-3\">\n            <h1 className=\"text-2xl text-black font-semibold\">Dashboard</h1>\n          </div>\n          <button className=\"rounded-full bg-gray-100 p-2 hover:bg-gray-200 transition-colors\">\n            <CalendarDays className=\"h-7 w-6 text-green-400\" />\n          </button>\n        </header>\n\n        {/* Main Content */}\n        <main className=\"p-4 h-full overflow-auto\">\n          {/* Stats Cards */}\n          <BookingStats />\n\n          {/* Dashboard Content */}\n          <div className=\"mt-6 p-6 bg-white rounded-lg shadow-sm\">\n            <h2 className=\"text-xl font-semibold mb-4\">Welcome to Marriage Hall Management System</h2>\n            <p className=\"text-gray-600\">\n              Use the sidebar to navigate between different sections:\n            </p>\n            <ul className=\"mt-4 space-y-2 text-gray-600\">\n              <li>• <strong>Dashboard:</strong> Overview and statistics</li>\n              <li>• <strong>Booking:</strong> Manage hall bookings and calendar</li>\n              <li>• <strong>Customer:</strong> Customer management</li>\n              <li>• <strong>Settings:</strong> System settings</li>\n            </ul>\n          </div>\n        </main>\n      </div>\n    </>\n  )\n}\n"], "names": [], "mappings": ";;;;AAGA;AAEA,aAAa;AACb;AANA;;;;AAQe,SAAS;IAEtB,qBACE;kBACE,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAO,WAAU;;sCAChB,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAG,WAAU;0CAAoC;;;;;;;;;;;sCAEpD,8OAAC;4BAAO,WAAU;sCAChB,cAAA,8OAAC,sNAAA,CAAA,eAAY;gCAAC,WAAU;;;;;;;;;;;;;;;;;8BAK5B,8OAAC;oBAAK,WAAU;;sCAEd,8OAAC,6IAAA,CAAA,eAAY;;;;;sCAGb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,8OAAC;oCAAE,WAAU;8CAAgB;;;;;;8CAG7B,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;;gDAAG;8DAAE,8OAAC;8DAAO;;;;;;gDAAmB;;;;;;;sDACjC,8OAAC;;gDAAG;8DAAE,8OAAC;8DAAO;;;;;;gDAAiB;;;;;;;sDAC/B,8OAAC;;gDAAG;8DAAE,8OAAC;8DAAO;;;;;;gDAAkB;;;;;;;sDAChC,8OAAC;;gDAAG;8DAAE,8OAAC;8DAAO;;;;;;gDAAkB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO9C", "debugId": null}}, {"offset": {"line": 415, "column": 0}, "map": {"version": 3, "file": "calendar-days.js", "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/booking-management/booking-hall/node_modules/lucide-react/src/icons/calendar-days.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M8 2v4', key: '1cmpym' }],\n  ['path', { d: 'M16 2v4', key: '4m81vk' }],\n  ['rect', { width: '18', height: '18', x: '3', y: '4', rx: '2', key: '1hopcy' }],\n  ['path', { d: 'M3 10h18', key: '8toen8' }],\n  ['path', { d: 'M8 14h.01', key: '6423bh' }],\n  ['path', { d: 'M12 14h.01', key: '1etili' }],\n  ['path', { d: 'M16 14h.01', key: '1gbofw' }],\n  ['path', { d: 'M8 18h.01', key: 'lrp35t' }],\n  ['path', { d: 'M12 18h.01', key: 'mhygvu' }],\n  ['path', { d: 'M16 18h.01', key: 'kzsmim' }],\n];\n\n/**\n * @component @name CalendarDays\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNOCAydjQiIC8+CiAgPHBhdGggZD0iTTE2IDJ2NCIgLz4KICA8cmVjdCB3aWR0aD0iMTgiIGhlaWdodD0iMTgiIHg9IjMiIHk9IjQiIHJ4PSIyIiAvPgogIDxwYXRoIGQ9Ik0zIDEwaDE4IiAvPgogIDxwYXRoIGQ9Ik04IDE0aC4wMSIgLz4KICA8cGF0aCBkPSJNMTIgMTRoLjAxIiAvPgogIDxwYXRoIGQ9Ik0xNiAxNGguMDEiIC8+CiAgPHBhdGggZD0iTTggMThoLjAxIiAvPgogIDxwYXRoIGQ9Ik0xMiAxOGguMDEiIC8+CiAgPHBhdGggZD0iTTE2IDE4aC4wMSIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/calendar-days\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst CalendarDays = createLucideIcon('calendar-days', __iconNode);\n\nexport default CalendarDays;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACvC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAW,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACxC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ;QAAA,CAAA;YAAE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO;YAAM,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAK,GAAG,CAAK,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAI,CAAK,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC9E;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACzC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC1C;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAc,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC3C;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAc,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC3C;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC1C;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAc,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC3C;QAAC,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAE;YAAA,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAc,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAA;KAAA;CAC7C;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,YAAA,CAAe,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAiB,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 521, "column": 0}, "map": {"version": 3, "file": "star.js", "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/booking-management/booking-hall/node_modules/lucide-react/src/icons/star.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  [\n    'path',\n    {\n      d: 'M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z',\n      key: 'r04s7s',\n    },\n  ],\n];\n\n/**\n * @component @name Star\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTEuNTI1IDIuMjk1YS41My41MyAwIDAgMSAuOTUgMGwyLjMxIDQuNjc5YTIuMTIzIDIuMTIzIDAgMCAwIDEuNTk1IDEuMTZsNS4xNjYuNzU2YS41My41MyAwIDAgMSAuMjk0LjkwNGwtMy43MzYgMy42MzhhMi4xMjMgMi4xMjMgMCAwIDAtLjYxMSAxLjg3OGwuODgyIDUuMTRhLjUzLjUzIDAgMCAxLS43NzEuNTZsLTQuNjE4LTIuNDI4YTIuMTIyIDIuMTIyIDAgMCAwLTEuOTczIDBMNi4zOTYgMjEuMDFhLjUzLjUzIDAgMCAxLS43Ny0uNTZsLjg4MS01LjEzOWEyLjEyMiAyLjEyMiAwIDAgMC0uNjExLTEuODc5TDIuMTYgOS43OTVhLjUzLjUzIDAgMCAxIC4yOTQtLjkwNmw1LjE2NS0uNzU1YTIuMTIyIDIuMTIyIDAgMCAwIDEuNTk3LTEuMTZ6IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/star\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Star = createLucideIcon('star', __iconNode);\n\nexport default Star;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QACE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACA;YACE,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YACH,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA;KACP;CAEJ;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAO,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,EAAQ,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 560, "column": 0}, "map": {"version": 3, "file": "dollar-sign.js", "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/booking-management/booking-hall/node_modules/lucide-react/src/icons/dollar-sign.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['line', { x1: '12', x2: '12', y1: '2', y2: '22', key: '7eqyqh' }],\n  ['path', { d: 'M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6', key: '1b0p4s' }],\n];\n\n/**\n * @component @name DollarSign\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8bGluZSB4MT0iMTIiIHgyPSIxMiIgeTE9IjIiIHkyPSIyMiIgLz4KICA8cGF0aCBkPSJNMTcgNUg5LjVhMy41IDMuNSAwIDAgMCAwIDdoNWEzLjUgMy41IDAgMCAxIDAgN0g2IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/dollar-sign\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst DollarSign = createLucideIcon('dollar-sign', __iconNode);\n\nexport default DollarSign;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAQ,CAAE;YAAA,CAAA,CAAA,EAAI,CAAM,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAI,CAAM,CAAA,CAAA,CAAA,CAAA;YAAA,EAAA,CAAI,CAAA,CAAA,CAAA,CAAK,CAAA;YAAA,CAAA,CAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAM;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACjE;QAAC,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAE;YAAA,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAqD,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAA;KAAA;CACpF;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,UAAA,CAAa,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAe,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}]}