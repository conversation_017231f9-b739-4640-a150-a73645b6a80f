"use client"

import { useState, useMemo, useRef, useEffect } from "react"
import { Bell, Calendar, CreditCard, DollarSign, Filter, ChevronDown, Check, User, Clock } from "lucide-react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Tabs, Ta<PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { ScrollArea } from "@/components/ui/scroll-area"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"

// Sample data
const notificationsData = [
  {
    id: 1,
    type: "New Booking",
    description: "<PERSON> booked a haircut appointment",
    time: "2 minutes ago",
    category: "Bookings",
    status: "Unread",
    icon: Calendar,
    borderColor: "blue-500",
    iconBg: "blue-100",
    iconColor: "blue-500",
    badge: "New",
  },
  {
    id: 2,
    type: "Payment Received",
    description: "Payment of $150 received from <PERSON>",
    time: "5 minutes ago",
    category: "Payments",
    status: "Read",
    icon: CreditCard,
    borderColor: "green-500",
    iconBg: "green-100",
    iconColor: "green-500",
    badge: "Paid",
  },
  {
    id: 3,
    type: "Appointment Reminder",
    description: "Reminder: <PERSON>'s appointment in 1 hour",
    time: "10 minutes ago",
    category: "Reminders",
    status: "Unread",
    icon: Clock,
    borderColor: "orange-500",
    iconBg: "orange-100",
    iconColor: "orange-500",
    badge: "Soon",
  },
  {
    id: 4,
    type: "New Customer",
    description: "Emma Davis registered as a new customer",
    time: "15 minutes ago",
    category: "Customers",
    status: "Read",
    icon: User,
    borderColor: "purple-500",
    iconBg: "purple-100",
    iconColor: "purple-500",
    badge: "New",
  },
]

const bookingsData = [
  {
    id: 1,
    name: "John Doe",
    service: "Haircut & Styling",
    duration: "10:00 AM - 11:00 AM",
    category: "Bookings",
    icon: User,
    borderColor: "blue-500",
    iconBg: "blue-100",
    iconColor: "blue-500",
    badge: "10:00 AM",
  },
  {
    id: 2,
    name: "Sarah Wilson",
    service: "Hair Coloring",
    duration: "11:30 AM - 1:30 PM",
    category: "Bookings",
    icon: User,
    borderColor: "green-500",
    iconBg: "green-100",
    iconColor: "green-500",
    badge: "11:30 AM",
  },
  {
    id: 3,
    name: "Mike Johnson",
    service: "Beard Trim",
    duration: "2:00 PM - 2:30 PM",
    category: "Bookings",
    icon: User,
    borderColor: "purple-500",
    iconBg: "purple-100",
    iconColor: "purple-500",
    badge: "2:00 PM",
  },
]

// Custom Checkbox Component
const CustomCheckbox = ({
  checked,
  onCheckedChange,
  id,
  className = "",
}: {
  checked: boolean
  onCheckedChange: (checked: boolean) => void
  id: string
  className?: string
}) => {
  return (
    <div className="relative">
      <input
        type="checkbox"
        id={id}
        checked={checked}
        onChange={(e) => onCheckedChange(e.target.checked)}
        className="sr-only"
        aria-describedby={`${id}-description`}
      />
      <div
        className={`h-4 w-4 rounded border-2 cursor-pointer transition-all duration-200 ${
          checked ? "bg-black border-black" : "bg-white border-gray-300 hover:border-gray-400"
        } ${className}`}
        onClick={() => onCheckedChange(!checked)}
        role="checkbox"
        aria-checked={checked}
        tabIndex={0}
        onKeyDown={(e) => {
          if (e.key === "Enter" || e.key === " ") {
            e.preventDefault()
            onCheckedChange(!checked)
          }
        }}
      >
        {checked && <Check className="h-3 w-3 text-white absolute top-0.5 left-0.5" />}
      </div>
    </div>
  )
}

// Color mapping
const colorMap = {
  "blue-500": { border: "border-l-blue-500", bg: "bg-blue-100", text: "text-blue-500" },
  "green-500": { border: "border-l-green-500", bg: "bg-green-100", text: "text-green-500" },
  "purple-500": { border: "border-l-purple-500", bg: "bg-purple-100", text: "text-purple-500" },
  "orange-500": { border: "border-l-orange-500", bg: "bg-orange-100", text: "text-orange-500" },
  "red-500": { border: "border-l-red-500", bg: "bg-red-100", text: "text-red-500" },
}

export default function DashboardPage() {
  const [selectedCategories, setSelectedCategories] = useState<string[]>([])
  const [selectedStatuses, setSelectedStatuses] = useState<string[]>([])
  const [isFilterOpen, setIsFilterOpen] = useState(false)
  const [activeTab, setActiveTab] = useState("notifications")

  const filterRef = useRef<HTMLDivElement>(null)
  const buttonRef = useRef<HTMLButtonElement>(null)

  // Close filter when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        filterRef.current &&
        buttonRef.current &&
        !filterRef.current.contains(event.target as Node) &&
        !buttonRef.current.contains(event.target as Node)
      ) {
        setIsFilterOpen(false)
      }
    }

    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === "Escape") {
        setIsFilterOpen(false)
      }
    }

    if (isFilterOpen) {
      document.addEventListener("mousedown", handleClickOutside)
      document.addEventListener("keydown", handleEscape)
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside)
      document.removeEventListener("keydown", handleEscape)
    }
  }, [isFilterOpen])

  // Memoized counts
  const categoryCounts = useMemo(() => {
    const counts: Record<string, number> = {}
    notificationsData.forEach((item) => {
      counts[item.category] = (counts[item.category] || 0) + 1
    })
    return counts
  }, [])

  const statusCounts = useMemo(() => {
    const counts: Record<string, number> = {}
    notificationsData.forEach((item) => {
      counts[item.status] = (counts[item.status] || 0) + 1
    })
    return counts
  }, [])

  // Filtered data
  const filteredNotifications = useMemo(() => {
    let filtered = notificationsData

    if (selectedCategories.length > 0) {
      filtered = filtered.filter((item) => selectedCategories.includes(item.category))
    }

    if (selectedStatuses.length > 0) {
      filtered = filtered.filter((item) => selectedStatuses.includes(item.status))
    }

    return filtered
  }, [selectedCategories, selectedStatuses])

  const filteredBookings = useMemo(() => {
    if (selectedCategories.length === 0) {
      return bookingsData
    }
    return bookingsData.filter((item) => selectedCategories.includes(item.category))
  }, [selectedCategories])

  // Filter handlers
  const handleCategoryChange = (category: string, checked: boolean) => {
    setSelectedCategories((prev) => (checked ? [...prev, category] : prev.filter((c) => c !== category)))
  }

  const handleStatusChange = (status: string, checked: boolean) => {
    setSelectedStatuses((prev) => (checked ? [...prev, status] : prev.filter((s) => s !== status)))
  }

  const clearAllFilters = () => {
    setSelectedCategories([])
    setSelectedStatuses([])
  }

  const activeFiltersCount = selectedCategories.length + selectedStatuses.length

  return (
    <div className="flex-1 overflow-hidden bg-gray-50">
      {/* Header */}
      <header className="flex h-16 items-center justify-between border-b bg-white px-6 shadow-sm">
        <div>
          <h1 className="text-2xl font-semibold text-gray-900">Dashboard</h1>
          <p className="text-sm text-gray-500">Welcome back! Here's what's happening today.</p>
        </div>
        <Button variant="ghost" size="icon" className="relative">
          <Bell className="h-5 w-5" />
          <span className="absolute -top-1 -right-1 h-3 w-3 bg-red-500 rounded-full"></span>
        </Button>
      </header>

      <main className="p-6 space-y-6 h-full overflow-auto">
        {/* Stats Cards */}
        <div className="grid grid-cols-1 gap-6 md:grid-cols-3">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Upcoming Bookings</p>
                  <p className="text-3xl font-bold text-gray-900">12</p>
                  <p className="text-sm text-green-600">+2 from yesterday</p>
                </div>
                <div className="h-12 w-12 bg-blue-100 rounded-lg flex items-center justify-center">
                  <Calendar className="h-6 w-6 text-blue-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Pending Payments</p>
                  <p className="text-3xl font-bold text-gray-900">$2,350</p>
                  <p className="text-sm text-orange-600">3 payments awaiting</p>
                </div>
                <div className="h-12 w-12 bg-orange-100 rounded-lg flex items-center justify-center">
                  <CreditCard className="h-6 w-6 text-orange-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Revenue</p>
                  <p className="text-3xl font-bold text-gray-900">$12,234</p>
                  <p className="text-sm text-green-600">+8% from last month</p>
                </div>
                <div className="h-12 w-12 bg-green-100 rounded-lg flex items-center justify-center">
                  <DollarSign className="h-6 w-6 text-green-600" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Activity Section */}
        <Card className="flex-1">
          <CardHeader className="flex flex-row items-center justify-between">
            <CardTitle>Recent Activity</CardTitle>

            {/* Filter Button */}
            <div className="relative">
              <Button
                ref={buttonRef}
                variant="outline"
                onClick={() => setIsFilterOpen(!isFilterOpen)}
                className="flex items-center gap-2"
              >
                <Filter className="h-4 w-4" />
                Filter
                {activeFiltersCount > 0 && (
                  <Badge variant="secondary" className="ml-1">
                    {activeFiltersCount}
                  </Badge>
                )}
                <ChevronDown className={`h-4 w-4 transition-transform ${isFilterOpen ? "rotate-180" : ""}`} />
              </Button>

              {/* Filter Dropdown */}
              {isFilterOpen && (
                <div
                  ref={filterRef}
                  className="absolute right-0 top-full mt-2 w-80 bg-white rounded-lg border shadow-lg z-50 max-h-96 overflow-y-auto"
                >
                  <div className="p-4 border-b">
                    <h3 className="font-semibold text-gray-900">Filter Options</h3>
                    <p className="text-sm text-gray-500">Customize your view</p>
                  </div>

                  <div className="p-4 space-y-6">
                    {/* Category Filters */}
                    <div>
                      <h4 className="font-medium text-gray-900 mb-3">Categories</h4>
                      <div className="space-y-2">
                        {["Bookings", "Payments", "Customers", "Reminders"].map((category) => (
                          <div
                            key={category}
                            className="flex items-center justify-between p-2 hover:bg-gray-50 rounded-md cursor-pointer"
                            onClick={() => handleCategoryChange(category, !selectedCategories.includes(category))}
                          >
                            <div className="flex items-center space-x-3">
                              <CustomCheckbox
                                id={`category-${category}`}
                                checked={selectedCategories.includes(category)}
                                onCheckedChange={(checked) => handleCategoryChange(category, checked)}
                              />
                              <label
                                htmlFor={`category-${category}`}
                                className="text-sm font-medium text-gray-700 cursor-pointer"
                              >
                                {category}
                              </label>
                            </div>
                            <Badge variant="outline">{categoryCounts[category] || 0}</Badge>
                          </div>
                        ))}
                      </div>
                    </div>

                    {/* Status Filters */}
                    <div>
                      <h4 className="font-medium text-gray-900 mb-3">Status</h4>
                      <div className="space-y-2">
                        {["Unread", "Read"].map((status) => (
                          <div
                            key={status}
                            className="flex items-center justify-between p-2 hover:bg-gray-50 rounded-md cursor-pointer"
                            onClick={() => handleStatusChange(status, !selectedStatuses.includes(status))}
                          >
                            <div className="flex items-center space-x-3">
                              <CustomCheckbox
                                id={`status-${status}`}
                                checked={selectedStatuses.includes(status)}
                                onCheckedChange={(checked) => handleStatusChange(status, checked)}
                              />
                              <label
                                htmlFor={`status-${status}`}
                                className="text-sm font-medium text-gray-700 cursor-pointer"
                              >
                                {status}
                              </label>
                            </div>
                            <Badge variant="outline">{statusCounts[status] || 0}</Badge>
                          </div>
                        ))}
                      </div>
                    </div>

                    {/* Clear Filters */}
                    {activeFiltersCount > 0 && (
                      <div className="pt-4 border-t">
                        <Button
                          variant="outline"
                          onClick={clearAllFilters}
                          className="w-full text-red-600 border-red-200 hover:bg-red-50 bg-transparent"
                        >
                          Clear All Filters
                        </Button>
                      </div>
                    )}
                  </div>
                </div>
              )}
            </div>
          </CardHeader>

          <CardContent>
            <Tabs value={activeTab} onValueChange={setActiveTab}>
              <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger value="notifications" className="flex items-center gap-2">
                  Notifications
                  <Badge variant="secondary">{filteredNotifications.length}</Badge>
                </TabsTrigger>
                <TabsTrigger value="bookings" className="flex items-center gap-2">
                  Today's Bookings
                  <Badge variant="secondary">{filteredBookings.length}</Badge>
                </TabsTrigger>
              </TabsList>

              {/* Notifications Tab */}
              <TabsContent value="notifications" className="mt-4">
                <div className="mb-4">
                  <p className="text-sm text-gray-500">
                    Showing {filteredNotifications.length} of {notificationsData.length} notifications
                  </p>
                </div>

                <ScrollArea className="h-[400px]">
                  <div className="space-y-3">
                    {filteredNotifications.length > 0 ? (
                      filteredNotifications.map((notification) => (
                        <div
                          key={notification.id}
                          className={`p-4 bg-white border-l-4 rounded-lg shadow-sm hover:shadow-md transition-shadow ${
                            colorMap[notification.borderColor as keyof typeof colorMap]?.border || "border-l-gray-500"
                          }`}
                        >
                          <div className="flex items-start gap-3">
                            <div
                              className={`p-2 rounded-full ${
                                colorMap[notification.iconBg as keyof typeof colorMap]?.bg || "bg-gray-100"
                              }`}
                            >
                              <notification.icon
                                className={`h-4 w-4 ${
                                  colorMap[notification.iconColor as keyof typeof colorMap]?.text || "text-gray-500"
                                }`}
                              />
                            </div>
                            <div className="flex-1 min-w-0">
                              <div className="flex items-start justify-between">
                                <div>
                                  <h3 className="font-medium text-gray-900">{notification.type}</h3>
                                  <p className="text-sm text-gray-600 mt-1">{notification.description}</p>
                                  <p className="text-xs text-gray-500 mt-1">{notification.time}</p>
                                </div>
                                {notification.badge && <Badge className="ml-2">{notification.badge}</Badge>}
                              </div>
                            </div>
                          </div>
                        </div>
                      ))
                    ) : (
                      <div className="text-center py-8">
                        <p className="text-gray-500">No notifications match your filters</p>
                        <Button variant="link" onClick={clearAllFilters} className="mt-2">
                          Clear filters to see all notifications
                        </Button>
                      </div>
                    )}
                  </div>
                </ScrollArea>
              </TabsContent>

              {/* Bookings Tab */}
              <TabsContent value="bookings" className="mt-4">
                <div className="mb-4">
                  <p className="text-sm text-gray-500">
                    You have {filteredBookings.length} bookings scheduled for today
                  </p>
                </div>

                <ScrollArea className="h-[400px]">
                  <div className="space-y-3">
                    {filteredBookings.length > 0 ? (
                      filteredBookings.map((booking) => (
                        <div
                          key={booking.id}
                          className={`p-4 bg-white border-l-4 rounded-lg shadow-sm hover:shadow-md transition-shadow ${
                            colorMap[booking.borderColor as keyof typeof colorMap]?.border || "border-l-gray-500"
                          }`}
                        >
                          <div className="flex items-start gap-3">
                            <div
                              className={`p-2 rounded-full ${
                                colorMap[booking.iconBg as keyof typeof colorMap]?.bg || "bg-gray-100"
                              }`}
                            >
                              <booking.icon
                                className={`h-4 w-4 ${
                                  colorMap[booking.iconColor as keyof typeof colorMap]?.text || "text-gray-500"
                                }`}
                              />
                            </div>
                            <div className="flex-1 min-w-0">
                              <div className="flex items-start justify-between">
                                <div>
                                  <h3 className="font-medium text-gray-900">{booking.name}</h3>
                                  <p className="text-sm text-gray-600 mt-1">{booking.service}</p>
                                  <p className="text-xs text-gray-500 mt-1">{booking.duration}</p>
                                </div>
                                <Badge className="ml-2">{booking.badge}</Badge>
                              </div>
                            </div>
                          </div>
                        </div>
                      ))
                    ) : (
                      <div className="text-center py-8">
                        <p className="text-gray-500">No bookings scheduled for today</p>
                      </div>
                    )}
                  </div>
                </ScrollArea>
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>
      </main>
    </div>
  )
}
