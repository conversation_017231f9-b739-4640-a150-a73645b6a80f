"use client"

import { useState } from "react"
import { CalendarDays } from "lucide-react"

// Components
import { BookingStats } from "./components/booking-stats"

export default function DashboardPage() {

  return (
    <>
      <div className="flex-1 overflow-hidden bg-gray-50">
        {/* Header */}
        <header className="flex h-16 items-end justify-between border-2 ml-3 mr-2 rounded-[12px] bg-white px-4 pb-4">
          <div className="flex items-center gap-3">
            <h1 className="text-2xl text-black font-semibold">Dashboard</h1>
          </div>
          <button className="rounded-full bg-gray-100 p-2 hover:bg-gray-200 transition-colors">
            <CalendarDays className="h-7 w-6 text-green-400" />
          </button>
        </header>

        {/* Main Content */}
        <main className="p-4 h-full overflow-auto">
          {/* Stats Cards */}
          <BookingStats />

          {/* Dashboard Content */}
          <div className="mt-6 p-6 bg-white rounded-lg shadow-sm">
            <h2 className="text-xl font-semibold mb-4">Welcome to Marriage Hall Management System</h2>
            <p className="text-gray-600">
              Use the sidebar to navigate between different sections:
            </p>
            <ul className="mt-4 space-y-2 text-gray-600">
              <li>• <strong>Dashboard:</strong> Overview and statistics</li>
              <li>• <strong>Booking:</strong> Manage hall bookings and calendar</li>
              <li>• <strong>Customer:</strong> Customer management</li>
              <li>• <strong>Settings:</strong> System settings</li>
            </ul>
          </div>
        </main>
      </div>
    </>
  )
}
