"use client"

interface AllBookingsModalProps {
  setShowAllBookingsModal: (show: boolean) => void
  bookings: any[]
  selectedEventType: string
  setSelectedEventType: (type: string) => void
  eventCategories: any[]
}

export function AllBookingsModal({
  setShowAllBookingsModal,
  bookings,
  selectedEventType,
  setSelectedEventType,
  eventCategories,
}: AllBookingsModalProps) {
  const getStatusColor = (status: string) => {
    switch (status) {
      case "confirmed":
        return "bg-green-100 text-green-800"
      case "pending":
        return "bg-yellow-100 text-yellow-800"
      case "cancelled":
        return "bg-red-100 text-red-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  const filteredBookings =
    selectedEventType === "all" ? bookings : bookings.filter((booking) => booking.eventType === selectedEventType)

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-6xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b">
          <div>
            <h3 className="text-xl font-semibold text-gray-900">All Bookings</h3>
            <p className="text-sm text-gray-600">Manage and view all booking records</p>
          </div>
          <button
            onClick={() => setShowAllBookingsModal(false)}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* Filter */}
        <div className="p-4 border-b">
          <div className="flex items-center justify-between">
            <span className="text-sm text-gray-500">Showing {bookings.length} total bookings</span>
          </div>
        </div>

        {/* Bookings List */}
        <div className="p-6 space-y-4 max-h-96 overflow-y-auto">
          {filteredBookings.map((booking, index) => {
            // Define border colors similar to calendar
            const borderColors = [
              { default: "#add8e6", hover: "#87ceeb" }, // light blue
              { default: "#ffb6c1", hover: "#ff69b4" }, // light pink
              { default: "#ffffe0", hover: "#fdfd96" }, // light yellow
              { default: "#90EE90", hover: "#32CD32" }, // light green
            ]

            const colorIndex = index % borderColors.length
            const borderColor = borderColors[colorIndex]

            return (
              <div
                key={booking.id}
                className="border-2 rounded-lg p-3 hover:shadow-md transition-all duration-200"
                style={{ borderColor: borderColor.default }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.borderColor = borderColor.hover
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.borderColor = borderColor.default
                }}
              >
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-1">
                      <h4 className="font-semibold text-gray-900 text-sm">{booking.customerName}</h4>
                      <span
                        className={`px-2 py-0.5 rounded-full text-xs font-medium ${getStatusColor(booking.status)}`}
                      >
                        {booking.status}
                      </span>
                    </div>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-2 text-xs text-gray-600">
                      <div>
                        <span className="font-medium">Event:</span> {booking.eventType}
                      </div>
                      <div>
                        <span className="font-medium">Date:</span> {booking.date}
                      </div>
                      <div>
                        <span className="font-medium">Time:</span> {booking.time}
                      </div>
                      <div>
                        <span className="font-medium">Hall:</span> {booking.hall}
                      </div>
                      <div>
                        <span className="font-medium">Guests:</span> {booking.guests}
                      </div>
                      <div>
                        <span className="font-medium">Amount:</span> ₹{booking.amount.toLocaleString()}
                      </div>
                    </div>
                  </div>
                  <div className="flex gap-1">
                    <button className="px-2 py-1 text-xs bg-blue-100 text-blue-700 rounded hover:bg-blue-200 transition-colors">
                      Edit
                    </button>
                    <button className="px-2 py-1 text-xs bg-red-100 text-red-700 rounded hover:bg-red-200 transition-colors">
                      Delete
                    </button>
                  </div>
                </div>
              </div>
            )
          })}
        </div>

        {/* Footer */}
        <div className="p-6 border-t bg-gray-50 flex justify-end">
          <button
            onClick={() => setShowAllBookingsModal(false)}
            className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            Close
          </button>
        </div>
      </div>
    </div>
  )
}
