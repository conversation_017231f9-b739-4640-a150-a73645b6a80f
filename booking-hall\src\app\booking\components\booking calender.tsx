"use client"

import { useState } from "react"
import { CalendarUtils } from "../utils/calender-utils"

interface BookingCalendarProps {
  currentCalendarMonth: number
  setCurrentCalendarMonth: (month: number | ((prev: number) => number)) => void
  selectedDate: string
  setSelectedCalendarDate: (date: string | null) => void
  bookings: any[]
}

export function BookingCalendar({
  currentCalendarMonth,
  setCurrentCalendarMonth,
  selectedDate,
  setSelectedCalendarDate,
  bookings,
}: BookingCalendarProps) {
  const [clickedBookedDate, setClickedBookedDate] = useState<string | null>(null)

  // Define border colors for each calendar
  const calendarBorders = [
    {
      default: "#add8e6", // light blue
      hover: "#87ceeb", // darker blue
    },
    {
      default: "#ffb6c1", // light pink
      hover: "#ff69b4", // darker pink
    },
    {
     default: "#d0f0c0", // light green
hover: "#a3d977",   // darker green
    },
  ]

  return (
    <div className="bg-white rounded-lg border border-gray-200 p-6 mb-6">
      {/* Month Navigation */}
      <div className="flex items-center justify-between mb-4">
        <button
          onClick={() => setCurrentCalendarMonth((prev) => prev - 1)}
          className="px-3 py-1 rounded bg-gray-100 hover:bg-gray-200 text-gray-700"
        >
          Previous
        </button>
        <div className="text-xl font-bold text-gray-800">
          {(() => {
            const today = new Date()
            const startMonth = new Date(today.getFullYear(), today.getMonth() + currentCalendarMonth, 1)
            const endMonth = new Date(today.getFullYear(), today.getMonth() + currentCalendarMonth + 2, 1)
            return `${startMonth.toLocaleDateString("en-US", { month: "long", year: "numeric" })} - ${endMonth.toLocaleDateString("en-US", { month: "long", year: "numeric" })}`
          })()}
        </div>
        <button
          onClick={() => setCurrentCalendarMonth((prev) => prev + 1)}
          className="px-3 py-1 rounded bg-gray-100 hover:bg-gray-200 text-gray-700"
        >
          Next
        </button>
      </div>

      {/* 3 Months Calendar Grid */}
      <div className="flex flex-col md:flex-row gap-6">
        {[0, 1, 2].map((offset) => {
          const { dates, month, year } = CalendarUtils.generateCalendarDates(currentCalendarMonth + offset)
          const borderColors = calendarBorders[offset]

          return (
            <div
              key={offset}
              className="flex-1 min-w-[220px] bg-white rounded-lg shadow p-3 transition-all duration-200 group"
              style={{
                border: `2px solid ${borderColors.default}`,
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.borderColor = borderColors.hover
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.borderColor = borderColors.default
              }}
            >
              <div className="text-center font-bold bg-gray-200 text-gray-800 mb-2 py-2 rounded">
                {new Date(year, month, 1).toLocaleDateString("en-US", { month: "long", year: "numeric" })}
              </div>
              <div className="grid grid-cols-7 gap-2 mb-2">
                {["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"].map((day) => (
                  <div key={day} className="text-center text-xs font-medium text-gray-500 py-1">
                    {day}
                  </div>
                ))}
              </div>
              <div className="grid grid-cols-7 gap-2">
                {dates.map((dateInfo, i) => {
                  const hasBooking = bookings.some((booking) => booking.date === dateInfo.dateStr)
                  const isClicked = clickedBookedDate === dateInfo.dateStr
                  const isToday = new Date().toDateString() === dateInfo.date.toDateString()

                  return (
                    <div key={i} className="relative">
                      <button
                        type="button"
                        onClick={() => {
                          if (hasBooking) {
                            // If clicking the same date, toggle it off, otherwise set new date
                            setClickedBookedDate(isClicked ? null : dateInfo.dateStr)
                          } else {
                            // If clicking non-booked date, clear selection
                            setClickedBookedDate(null)
                          }
                        }}
                        disabled={dateInfo.isPast}
                        className={`w-full p-2 text-xs rounded-lg border transition-colors relative flex flex-col items-center justify-center min-h-[32px]
                          ${
                            !dateInfo.isCurrentMonth
                              ? "text-gray-400 border-gray-200"
                              : dateInfo.isPast
                                ? "text-gray-300 cursor-not-allowed border-gray-200"
                                : isClicked
                                  ? "bg-orange-200 text-orange-900 border-orange-300"
                                  : isToday
                                    ? "bg-orange-100 text-orange-800 border-orange-300"
                                    : hasBooking
                                      ? "bg-green-100 text-green-800 border-green-300 hover:bg-green-200 cursor-pointer"
                                      : "hover:bg-gray-50 text-gray-700 border-gray-300 cursor-pointer"
                          }
                        `}
                      >
                        {dateInfo.date.getDate()}
                        {/* Booking indicator dots */}
                        {hasBooking && dateInfo.isCurrentMonth && (
                          <span className="absolute bottom-1 right-1 flex gap-0.5">
                            <span className="w-1 h-1 bg-gray-600 rounded-full"></span>
                            <span className="w-1 h-1 bg-gray-600 rounded-full"></span>
                            <span className="w-1 h-1 bg-gray-600 rounded-full"></span>
                          </span>
                        )}
                      </button>
                      {/* Inline "Booked" text under clicked date */}
                      {isClicked && hasBooking && (
                        <div className="absolute top-full left-1/2 transform -translate-x-1/2 mt-1 z-10">
                          <span className="text-xs bg-orange-200 text-orange-800 px-2 py-1 rounded shadow-sm whitespace-nowrap">
                            Booked
                          </span>
                        </div>
                      )}
                    </div>
                  )
                })}
              </div>
            </div>
          )
        })}
      </div>
    </div>
  )
}
