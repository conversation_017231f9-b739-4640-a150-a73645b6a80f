"use client"

import type React from "react"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"

export function BookingForm() {
  const [formData, setFormData] = useState({
    customerName: "",
    phone: "",
    email: "",
    service: "",
    date: "",
    time: "",
    notes: "",
    separation: "",
    staff: "",
  })

  const handleFormSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    console.log("Form submitted:", formData)
    // Handle form submission logic here
  }

  const handleInputChange = (field: string, value: string) => {
    setFormData((prev) => ({ ...prev, [field]: value }))
  }

  return (
    <div className="max-w-2xl mx-auto">
      <h3 className="text-lg font-semibold mb-6">Create New Booking</h3>
      <form onSubmit={handleFormSubmit} className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <Label htmlFor="customerName">Customer Name</Label>
            <Input
              id="customerName"
              value={formData.customerName}
              onChange={(e) => handleInputChange("customerName", e.target.value)}
              placeholder="Enter customer name"
              required
            />
          </div>
          <div>
            <Label htmlFor="phone">Phone Number</Label>
            <Input
              id="phone"
              value={formData.phone}
              onChange={(e) => handleInputChange("phone", e.target.value)}
              placeholder="****** 567 8900"
              required
            />
          </div>
        </div>

        <div>
          <Label htmlFor="email">Email Address</Label>
          <Input
            id="email"
            type="email"
            value={formData.email}
            onChange={(e) => handleInputChange("email", e.target.value)}
            placeholder="<EMAIL>"
            required
          />
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <Label htmlFor="service">Service</Label>
            <Select value={formData.service} onValueChange={(value) => handleInputChange("service", value)}>
              <SelectTrigger>
                <SelectValue placeholder="Select a service" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="wedding">Wedding Ceremony</SelectItem>
                <SelectItem value="engagement">Engagement Function</SelectItem>
                <SelectItem value="mehndi">Mehndi Ceremony</SelectItem>
                <SelectItem value="valima">Valima Reception</SelectItem>
                <SelectItem value="birthday">Birthday Party</SelectItem>
                <SelectItem value="other">Other Events</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div>
            <Label htmlFor="time">Preferred Time</Label>
            <Select value={formData.time} onValueChange={(value) => handleInputChange("time", value)}>
              <SelectTrigger>
                <SelectValue placeholder="Select time slot" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="morning">Morning (9:00 AM - 1:00 PM)</SelectItem>
                <SelectItem value="afternoon">Afternoon (2:00 PM - 6:00 PM)</SelectItem>
                <SelectItem value="evening">Evening (6:00 PM - 11:00 PM)</SelectItem>
                <SelectItem value="night">Night (7:00 PM - 12:00 AM)</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        <div>
          <Label htmlFor="date">Preferred Date</Label>
          <Input
            id="date"
            type="date"
            value={formData.date}
            onChange={(e) => handleInputChange("date", e.target.value)}
            required
          />
        </div>

        <div>
          <Label htmlFor="notes">Additional Notes</Label>
          <Textarea
            id="notes"
            value={formData.notes}
            onChange={(e) => handleInputChange("notes", e.target.value)}
            placeholder="Any special requests or notes..."
            rows={4}
          />
        </div>

        {/* Additional Commitment Section */}
        <div>
          <h4 className="text-md font-semibold mb-4">Additional Commitment</h4>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <Label htmlFor="separation">Male/Female Separation</Label>
              <Select value={formData.separation} onValueChange={(value) => handleInputChange("separation", value)}>
                <SelectTrigger>
                  <SelectValue placeholder="Select separation option" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="yes">Yes</SelectItem>
                  <SelectItem value="no">No</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label htmlFor="staff">Male/Female Staff</Label>
              <Select value={formData.staff} onValueChange={(value) => handleInputChange("staff", value)}>
                <SelectTrigger>
                  <SelectValue placeholder="Select staff preference" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="male">Male Staff</SelectItem>
                  <SelectItem value="female">Female Staff</SelectItem>
                  <SelectItem value="both">Both</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>

        <Button type="submit" className="w-full bg-blue-600 hover:bg-blue-700">
          Create Booking
        </Button>
      </form>
    </div>
  )
}
