"use client"

import type React from "react"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"

export function BookingForm() {
  const [formData, setFormData] = useState({
    customerName: "",
    phone: "",
    email: "",
    eventType: "",
    date: "",
    time: "",
    guests: "",
    hall: "",
    notes: "",
  })

  const handleFormSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    console.log("Form submitted:", formData)
    // Handle form submission logic here
  }

  const handleInputChange = (field: string, value: string) => {
    setFormData((prev) => ({ ...prev, [field]: value }))
  }

  return (
    <div className="max-w-2xl mx-auto">
      <h3 className="text-lg font-semibold mb-6">Create New Booking</h3>
      <form onSubmit={handleFormSubmit} className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <Label htmlFor="customerName">Customer Name</Label>
            <Input
              id="customerName"
              value={formData.customerName}
              onChange={(e) => handleInputChange("customerName", e.target.value)}
              placeholder="Enter customer name"
              required
            />
          </div>
          <div>
            <Label htmlFor="phone">Phone Number</Label>
            <Input
              id="phone"
              value={formData.phone}
              onChange={(e) => handleInputChange("phone", e.target.value)}
              placeholder="****** 567 8900"
              required
            />
          </div>
        </div>

        <div>
          <Label htmlFor="email">Email Address</Label>
          <Input
            id="email"
            type="email"
            value={formData.email}
            onChange={(e) => handleInputChange("email", e.target.value)}
            placeholder="<EMAIL>"
            required
          />
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <Label htmlFor="eventType">Event Type</Label>
            <Select value={formData.eventType} onValueChange={(value) => handleInputChange("eventType", value)}>
              <SelectTrigger>
                <SelectValue placeholder="Select event type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="wedding">Wedding</SelectItem>
                <SelectItem value="engagement">Engagement</SelectItem>
                <SelectItem value="valima">Valima</SelectItem>
                <SelectItem value="birthday">Birthday Party</SelectItem>
                <SelectItem value="aqiqa">Aqiqa</SelectItem>
                <SelectItem value="milad">Milad</SelectItem>
                <SelectItem value="anniversary">Anniversary</SelectItem>
                <SelectItem value="other">Other</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div>
            <Label htmlFor="time">Preferred Time</Label>
            <Select value={formData.time} onValueChange={(value) => handleInputChange("time", value)}>
              <SelectTrigger>
                <SelectValue placeholder="Select time slot" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="morning">Morning (10:00 AM - 2:00 PM)</SelectItem>
                <SelectItem value="afternoon">Afternoon (2:00 PM - 6:00 PM)</SelectItem>
                <SelectItem value="evening">Evening (6:00 PM - 11:00 PM)</SelectItem>
                <SelectItem value="night">Night (7:00 PM - 12:00 AM)</SelectItem>
                <SelectItem value="full-day">Full Day (10:00 AM - 11:00 PM)</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <Label htmlFor="guests">Number of Guests</Label>
            <Input
              id="guests"
              type="number"
              value={formData.guests}
              onChange={(e) => handleInputChange("guests", e.target.value)}
              placeholder="e.g., 200"
              required
            />
          </div>
          <div>
            <Label htmlFor="hall">Hall Selection</Label>
            <Select value={formData.hall} onValueChange={(value) => handleInputChange("hall", value)}>
              <SelectTrigger>
                <SelectValue placeholder="Select hall" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="grand-hall-a">Grand Hall A (300 capacity)</SelectItem>
                <SelectItem value="royal-hall-b">Royal Hall B (200 capacity)</SelectItem>
                <SelectItem value="premium-hall-c">Premium Hall C (250 capacity)</SelectItem>
                <SelectItem value="deluxe-hall-d">Deluxe Hall D (150 capacity)</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        <div>
          <Label htmlFor="date">Preferred Date</Label>
          <Input
            id="date"
            type="date"
            value={formData.date}
            onChange={(e) => handleInputChange("date", e.target.value)}
            required
          />
        </div>

        <div>
          <Label htmlFor="notes">Additional Notes</Label>
          <Textarea
            id="notes"
            value={formData.notes}
            onChange={(e) => handleInputChange("notes", e.target.value)}
            placeholder="Any special requests or notes..."
            rows={4}
          />
        </div>

        <Button type="submit" className="w-full bg-blue-600 hover:bg-blue-700">
          Create Booking
        </Button>
      </form>
    </div>
  )
}
