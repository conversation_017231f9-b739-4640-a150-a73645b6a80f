"use client"

interface DateDetailsModalProps {
  selectedCalendarDate: string
  setSelectedCalendarDate: (date: string | null) => void
  bookings: any[]
}

export function DateDetailsModal({ selectedCalendarDate, setSelectedCalendarDate, bookings }: DateDetailsModalProps) {
  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-md w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b">
          <div>
            <h3 className="text-lg font-semibold text-gray-900">Date Details</h3>
            <p className="text-sm text-gray-600">
              {new Date(selectedCalendarDate).toLocaleDateString("en-US", {
                weekday: "long",
                year: "numeric",
                month: "long",
                day: "numeric",
              })}
            </p>
          </div>
          <button
            onClick={() => setSelectedCalendarDate(null)}
            className="text-gray-400 hover:text-gray-600 transition-colors p-1"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* Content */}
        <div className="p-4">
          {(() => {
            const dateBookings = bookings.filter((b) => b.date === selectedCalendarDate)
            if (dateBookings.length === 0) {
              return <div className="text-gray-500">No bookings for this date.</div>
            }
            return dateBookings.map((booking) => (
              <div key={booking.id} className="mb-4 border-b pb-2 last:border-b-0 last:pb-0">
                <div className="font-medium text-gray-800">{booking.customerName}</div>
                <div className="text-sm text-gray-600">
                  {booking.eventType} | {booking.hall}
                </div>
                <div className="text-xs text-gray-500">Guests: {booking.guests}</div>
                <div className="text-xs text-gray-500">Time: {booking.time}</div>
                <div className="text-xs text-gray-500">Status: {booking.status}</div>
                <div className="text-xs text-gray-500">Amount: ₹{booking.amount.toLocaleString()}</div>
              </div>
            ))
          })()}
        </div>

        {/* Footer Actions */}
        <div className="p-4 border-t bg-gray-50 flex gap-2">
          <button
            onClick={() => setSelectedCalendarDate(null)}
            className="flex-1 px-4 py-2 bg-orange-200 text-white rounded-lg hover:bg-orange-300 transition-colors font-medium"
          >
            Close
          </button>
        </div>
      </div>
    </div>
  )
}
