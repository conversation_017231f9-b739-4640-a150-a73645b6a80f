/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [
  ["path", { d: "M14 4v16H3a1 1 0 0 1-1-1V5a1 1 0 0 1 1-1z", key: "1m5n7q" }],
  ["circle", { cx: "14", cy: "12", r: "8", key: "1pag6k" }]
];
const RectangleCircle = createLucideIcon("rectangle-circle", __iconNode);

export { __iconNode, RectangleCircle as default };
//# sourceMappingURL=rectangle-circle.js.map
